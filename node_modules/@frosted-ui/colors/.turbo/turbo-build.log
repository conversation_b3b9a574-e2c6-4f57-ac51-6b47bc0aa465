
> @frosted-ui/colors@0.0.0 build /home/<USER>/work/frosted-ui/frosted-ui/packages/frosted-ui-colors
> pnpm clean && pnpm rollup -c && pnpm build-css-modules


> @frosted-ui/colors@0.0.0 clean /home/<USER>/work/frosted-ui/frosted-ui/packages/frosted-ui-colors
> rm -f *.css index.js index.mjs


src/index.ts → index.js, index.mjs...
created index.js, index.mjs in 1.7s

> @frosted-ui/colors@0.0.0 build-css-modules /home/<USER>/work/frosted-ui/frosted-ui/packages/frosted-ui-colors
> node ./scripts/build-css-modules.js

