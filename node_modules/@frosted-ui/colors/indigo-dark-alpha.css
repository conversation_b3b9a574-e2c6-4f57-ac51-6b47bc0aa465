.dark, .dark-theme {
  --indigo-a1: #0000ff0f;
  --indigo-a2: #3f2dfe1c;
  --indigo-a3: #5230ff4b;
  --indigo-a4: #571fff72;
  --indigo-a5: #6030ff85;
  --indigo-a6: #6741ff95;
  --indigo-a7: #6e4cffae;
  --indigo-a8: #704ffed4;
  --indigo-a9: #6619fff8;
  --indigo-a10: #8869ffd7;
  --indigo-a11: #ada8ff;
  --indigo-a12: #ddddff;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --indigo-a1: color(display-p3 0 0 0.9961 / 0.055);
      --indigo-a2: color(display-p3 0.2627 0.1843 1 / 0.101);
      --indigo-a3: color(display-p3 0.3059 0.1922 1 / 0.282);
      --indigo-a4: color(display-p3 0.3255 0.1333 1 / 0.425);
      --indigo-a5: color(display-p3 0.3529 0.1961 0.9961 / 0.5);
      --indigo-a6: color(display-p3 0.4 0.2706 1 / 0.559);
      --indigo-a7: color(display-p3 0.4275 0.3137 1 / 0.652);
      --indigo-a8: color(display-p3 0.4392 0.3294 1 / 0.795);
      --indigo-a9: color(display-p3 0.3804 0.1216 1 / 0.929);
      --indigo-a10: color(display-p3 0.510 0.410 1 / 0.84);
      --indigo-a11: color(display-p3 0.6902 0.6745 1 / 0.975);
      --indigo-a12: color(display-p3 0.8784 0.8784 1 / 0.988);
    }
  }
}