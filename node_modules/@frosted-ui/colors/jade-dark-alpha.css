.dark, .dark-theme {
  --jade-a1: #00de4505;
  --jade-a2: #27fba60c;
  --jade-a3: #02f99920;
  --jade-a4: #00ffaa2d;
  --jade-a5: #11ffb63b;
  --jade-a6: #34ffc24b;
  --jade-a7: #45fdc75e;
  --jade-a8: #48ffcf75;
  --jade-a9: #38feca9d;
  --jade-a10: #31fec7ab;
  --jade-a11: #21fec0d6;
  --jade-a12: #b8ffe1ef;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --jade-a1: color(display-p3 0 0.992 0.298 / 0.017);
      --jade-a2: color(display-p3 0.318 0.988 0.651 / 0.047);
      --jade-a3: color(display-p3 0.267 1 0.667 / 0.118);
      --jade-a4: color(display-p3 0.275 0.996 0.702 / 0.173);
      --jade-a5: color(display-p3 0.361 1 0.741 / 0.227);
      --jade-a6: color(display-p3 0.439 1 0.796 / 0.286);
      --jade-a7: color(display-p3 0.49 1 0.804 / 0.362);
      --jade-a8: color(display-p3 0.506 1 0.835 / 0.45);
      --jade-a9: color(display-p3 0.478 0.996 0.816 / 0.606);
      --jade-a10: color(display-p3 0.478 1 0.816 / 0.656);
      --jade-a11: color(display-p3 0.4 0.835 0.656);
      --jade-a12: color(display-p3 0.734 0.934 0.838);
    }
  }
}