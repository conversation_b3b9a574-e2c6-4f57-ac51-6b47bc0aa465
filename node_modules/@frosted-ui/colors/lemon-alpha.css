:root, .light, .light-theme {
  --lemon-a1: #80aa0006;
  --lemon-a2: #a5d20011;
  --lemon-a3: #beee003a;
  --lemon-a4: #b8e60059;
  --lemon-a5: #b1d80075;
  --lemon-a6: #9fc00084;
  --lemon-a7: #8ca80092;
  --lemon-a8: #879f00bd;
  --lemon-a9: #d7f100;
  --lemon-a10: #cbe400ef;
  --lemon-a11: #6f7d00;
  --lemon-a12: #212800e2;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --lemon-a1: color(display-p3 0.5137 0.6745 0.0235 / 0.024);
      --lemon-a2: color(display-p3 0.6902 0.8157 0.0078 / 0.063);
      --lemon-a3: color(display-p3 0.7569 0.9255 0.0039 / 0.208);
      --lemon-a4: color(display-p3 0.7255 0.8784 0.0039 / 0.314);
      --lemon-a5: color(display-p3 0.6863 0.8196 0.0039 / 0.412);
      --lemon-a6: color(display-p3 0.6118 0.7176 0.0039 / 0.471);
      --lemon-a7: color(display-p3 0.5294 0.6196 0 / 0.526);
      --lemon-a8: color(display-p3 0.4941 0.5765 0 / 0.667);
      --lemon-a9: color(display-p3 0.8039 0.9137 0 / 0.695);
      --lemon-a10: color(display-p3 0.7569 0.8549 0 / 0.702);
      --lemon-a11: color(display-p3 0.3569 0.4078 0 / 0.859);
      --lemon-a12: color(display-p3 0.1137 0.1373 0 / 0.867);
    }
  }
}