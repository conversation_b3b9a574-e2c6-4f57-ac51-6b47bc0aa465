.dark, .dark-theme {
  --lemon-a1: #00910002;
  --lemon-a2: #bcf40009;
  --lemon-a3: #d1fb0718;
  --lemon-a4: #d2fb0025;
  --lemon-a5: #d8fc0032;
  --lemon-a6: #ddfd2141;
  --lemon-a7: #dffe3d53;
  --lemon-a8: #e2ff4469;
  --lemon-a9: #e3fe05f5;
  --lemon-a10: #e5fe51e8;
  --lemon-a11: #e4fe4ee5;
  --lemon-a12: #f0feb8f5;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --lemon-a1: color(display-p3 0 0.8549 0 / 0.005);
      --lemon-a2: color(display-p3 0.7608 0.9922 0 / 0.034);
      --lemon-a3: color(display-p3 0.8706 0.9961 0.1529 / 0.093);
      --lemon-a4: color(display-p3 0.8627 1 0.1216 / 0.143);
      --lemon-a5: color(display-p3 0.8784 1 0.1686 / 0.194);
      --lemon-a6: color(display-p3 0.8902 0.9961 0.2706 / 0.253);
      --lemon-a7: color(display-p3 0.902 1 0.3608 / 0.324);
      --lemon-a8: color(display-p3 0.9059 1 0.3843 / 0.412);
      --lemon-a9: color(display-p3 0.9176 1 0.3255 / 0.954);
      --lemon-a10: color(display-p3 0.9216 0.9961 0.4392 / 0.904);
      --lemon-a11: color(display-p3 0.9216 1 0.4314 / 0.891);
      --lemon-a12: color(display-p3 0.9529 1 0.7529 / 0.958);
    }
  }
}