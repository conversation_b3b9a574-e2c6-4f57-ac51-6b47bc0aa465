:root, .light, .light-theme {
  --lime-a1: #00cc0005;
  --lime-a2: #16c0000c;
  --lime-a3: #16e20023;
  --lime-a4: #12e00039;
  --lime-a5: #10d30050;
  --lime-a6: #0dc30069;
  --lime-a7: #0ab30089;
  --lime-a8: #03af00bf;
  --lime-a9: #00d612f9;
  --lime-a10: #00cb00;
  --lime-a11: #008600;
  --lime-a12: #013000e7;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --lime-a1: color(display-p3 0.2157 0.8039 0.0196 / 0.02);
      --lime-a2: color(display-p3 0.1098 0.7333 0.0196 / 0.044);
      --lime-a3: color(display-p3 0.1882 0.851 0.0078 / 0.13);
      --lime-a4: color(display-p3 0.1725 0.851 0.0039 / 0.208);
      --lime-a5: color(display-p3 0.1647 0.7882 0.0039 / 0.291);
      --lime-a6: color(display-p3 0.149 0.7255 0.0039 / 0.381);
      --lime-a7: color(display-p3 0.1373 0.6627 0.0039 / 0.499);
      --lime-a8: color(display-p3 0.1294 0.6353 0 / 0.675);
      --lime-a9: color(display-p3 0.1608 0.7686 0 / 0.734);
      --lime-a10: color(display-p3 0.1686 0.7216 0 / 0.769);
      --lime-a11: color(display-p3 0.102 0.4392 0 / 0.859);
      --lime-a12: color(display-p3 0.0314 0.1647 0 / 0.887);
    }
  }
}