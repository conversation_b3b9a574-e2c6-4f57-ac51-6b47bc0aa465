.dark, .dark-theme {
  --lime-a1: #00bb0003;
  --lime-a2: #29f9120b;
  --lime-a3: #21f91920;
  --lime-a4: #0cfb0730;
  --lime-a5: #22fc223f;
  --lime-a6: #38fd354f;
  --lime-a7: #43fd4061;
  --lime-a8: #45ff4376;
  --lime-a9: #04fe19d5;
  --lime-a10: #00fe00c8;
  --lime-a11: #1eff28db;
  --lime-a12: #b2feadf6;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --lime-a1: color(display-p3 0 0.9412 0 / 0.009);
      --lime-a2: color(display-p3 0.3412 0.9804 0.1608 / 0.043);
      --lime-a3: color(display-p3 0.3686 1 0.2 / 0.118);
      --lime-a4: color(display-p3 0.3294 1 0.1765 / 0.181);
      --lime-a5: color(display-p3 0.3961 1 0.2627 / 0.24);
      --lime-a6: color(display-p3 0.4588 1 0.3294 / 0.303);
      --lime-a7: color(display-p3 0.4863 1 0.3608 / 0.374);
      --lime-a8: color(display-p3 0.502 1 0.3804 / 0.454);
      --lime-a9: color(display-p3 0.4549 1 0.3098 / 0.82);
      --lime-a10: color(display-p3 0.451 1 0.2824 / 0.769);
      --lime-a11: color(display-p3 0.4706 1 0.3294 / 0.845);
      --lime-a12: color(display-p3 0.7725 1 0.7137 / 0.954);
    }
  }
}