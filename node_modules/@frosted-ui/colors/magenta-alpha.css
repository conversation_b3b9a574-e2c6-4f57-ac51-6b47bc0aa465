:root, .light, .light-theme {
  --magenta-a1: #ff005503;
  --magenta-a2: #ff005509;
  --magenta-a3: #ff005518;
  --magenta-a4: #ff005826;
  --magenta-a5: #ff005235;
  --magenta-a6: #f4004e45;
  --magenta-a7: #e0004859;
  --magenta-a8: #d5004a73;
  --magenta-a9: #ff008d;
  --magenta-a10: #ef0081;
  --magenta-a11: #d40070;
  --magenta-a12: #6b0037;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --magenta-a1: color(display-p3 0.6745 0.0235 0.349 / 0.012);
      --magenta-a2: color(display-p3 0.7843 0.0235 0.349 / 0.036);
      --magenta-a3: color(display-p3 0.8314 0.0118 0.3137 / 0.091);
      --magenta-a4: color(display-p3 0.8392 0.0078 0.3294 / 0.146);
      --magenta-a5: color(display-p3 0.8431 0 0.2941 / 0.2);
      --magenta-a6: color(display-p3 0.8039 0.0039 0.2588 / 0.259);
      --magenta-a7: color(display-p3 0.7451 0.0039 0.2471 / 0.338);
      --magenta-a8: color(display-p3 0.7098 0.0039 0.2471 / 0.432);
      --magenta-a9: color(display-p3 0.898 0 0.4314 / 0.8);
      --magenta-a10: color(display-p3 0.8275 0 0.3882 / 0.816);
      --magenta-a11: color(display-p3 0.7137 0 0.3255 / 0.84);
      --magenta-a12: color(display-p3 0.3451 0 0.1608 / 0.942);
    }
  }
}