.dark, .dark-theme {
  --magenta-a1: #f1001208;
  --magenta-a2: #f4206612;
  --magenta-a3: #fb0c6c30;
  --magenta-a4: #ff006c49;
  --magenta-a5: #ff00775a;
  --magenta-a6: #fd1d846d;
  --magenta-a7: #fe368d8a;
  --magenta-a8: #fe3a92b8;
  --magenta-a9: #ff008d;
  --magenta-a10: #ff0089ef;
  --magenta-a11: #ff89b8;
  --magenta-a12: #ffcfe0;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --magenta-a1: color(display-p3 0.9608 0 0.0667 / 0.022);
      --magenta-a2: color(display-p3 1 0.2 0.4667 / 0.059);
      --magenta-a3: color(display-p3 1 0.1176 0.4745 / 0.164);
      --magenta-a4: color(display-p3 0.9961 0 0.4549 / 0.253);
      --magenta-a5: color(display-p3 0.9961 0.0784 0.502 / 0.316);
      --magenta-a6: color(display-p3 1 0.2118 0.5608 / 0.383);
      --magenta-a7: color(display-p3 1 0.298 0.5961 / 0.488);
      --magenta-a8: color(display-p3 1 0.3176 0.6157 / 0.656);
      --magenta-a9: color(display-p3 1 0.2157 0.5922 / 0.912);
      --magenta-a10: color(display-p3 1 0.2078 0.5804 / 0.853);
      --magenta-a11: color(display-p3 1 0.5961 0.7647 / 0.933);
      --magenta-a12: color(display-p3 0.9961 0.8431 0.902 / 0.967);
    }
  }
}