:root, .light, .light-theme {
  --mauve-a1: #55005503;
  --mauve-a2: #2b005506;
  --mauve-a3: #30004010;
  --mauve-a4: #20003618;
  --mauve-a5: #20003820;
  --mauve-a6: #14003527;
  --mauve-a7: #10003332;
  --mauve-a8: #08003145;
  --mauve-a9: #05001d73;
  --mauve-a10: #0500197d;
  --mauve-a11: #0400119c;
  --mauve-a12: #020008e0;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --mauve-a1: color(display-p3 0.349 0.024 0.349 / 0.012);
      --mauve-a2: color(display-p3 0.184 0.024 0.349 / 0.024);
      --mauve-a3: color(display-p3 0.129 0.008 0.255 / 0.063);
      --mauve-a4: color(display-p3 0.094 0.012 0.216 / 0.095);
      --mauve-a5: color(display-p3 0.098 0.008 0.224 / 0.126);
      --mauve-a6: color(display-p3 0.055 0.004 0.18 / 0.153);
      --mauve-a7: color(display-p3 0.067 0.008 0.184 / 0.197);
      --mauve-a8: color(display-p3 0.02 0.004 0.176 / 0.271);
      --mauve-a9: color(display-p3 0.02 0.004 0.106 / 0.451);
      --mauve-a10: color(display-p3 0.012 0.004 0.09 / 0.491);
      --mauve-a11: color(display-p3 0.016 0 0.059 / 0.612);
      --mauve-a12: color(display-p3 0.008 0 0.027 / 0.879);
    }
  }
}