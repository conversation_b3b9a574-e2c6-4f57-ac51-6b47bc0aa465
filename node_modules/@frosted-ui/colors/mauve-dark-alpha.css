.dark, .dark-theme {
  --mauve-a1: #00000000;
  --mauve-a2: #f5f4f609;
  --mauve-a3: #ebeaf814;
  --mauve-a4: #eee5f81d;
  --mauve-a5: #efe6fe25;
  --mauve-a6: #f1e6fd30;
  --mauve-a7: #eee9ff40;
  --mauve-a8: #eee7ff5d;
  --mauve-a9: #eae6fd6e;
  --mauve-a10: #ece9fd7c;
  --mauve-a11: #f5f1ffb7;
  --mauve-a12: #fdfdffef;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --mauve-a1: color(display-p3 0 0 0 / 0);
      --mauve-a2: color(display-p3 0.996 0.992 1 / 0.034);
      --mauve-a3: color(display-p3 0.937 0.933 0.992 / 0.077);
      --mauve-a4: color(display-p3 0.957 0.918 0.996 / 0.111);
      --mauve-a5: color(display-p3 0.937 0.906 0.996 / 0.145);
      --mauve-a6: color(display-p3 0.953 0.925 0.996 / 0.183);
      --mauve-a7: color(display-p3 0.945 0.929 1 / 0.246);
      --mauve-a8: color(display-p3 0.937 0.918 1 / 0.361);
      --mauve-a9: color(display-p3 0.933 0.918 1 / 0.424);
      --mauve-a10: color(display-p3 0.941 0.925 1 / 0.479);
      --mauve-a11: color(display-p3 0.965 0.961 1 / 0.712);
      --mauve-a12: color(display-p3 0.992 0.992 1 / 0.937);
    }
  }
}