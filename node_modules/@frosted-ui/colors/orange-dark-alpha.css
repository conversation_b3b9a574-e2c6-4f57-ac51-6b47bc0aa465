.dark, .dark-theme {
  --orange-a1: #ec000007;
  --orange-a2: #ff44220f;
  --orange-a3: #ff28002d;
  --orange-a4: #fe0e0046;
  --orange-a5: #ff230058;
  --orange-a6: #ff401469;
  --orange-a7: #ff583183;
  --orange-a8: #ff5d39af;
  --orange-a9: #ff4716fa;
  --orange-a10: #ff3a00ea;
  --orange-a11: #ff9275;
  --orange-a12: #ffd2c6;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --orange-a1: color(display-p3 0.9608 0 0 / 0.022);
      --orange-a2: color(display-p3 0.9922 0.298 0.1451 / 0.051);
      --orange-a3: color(display-p3 1 0.2196 0.0196 / 0.156);
      --orange-a4: color(display-p3 1 0.149 0 / 0.244);
      --orange-a5: color(display-p3 1 0.2196 0.0196 / 0.311);
      --orange-a6: color(display-p3 1 0.3294 0.149 / 0.374);
      --orange-a7: color(display-p3 1 0.4118 0.2588 / 0.471);
      --orange-a8: color(display-p3 1 0.4353 0.2941 / 0.631);
      --orange-a9: color(display-p3 0.9961 0.3608 0.1961 / 0.9);
      --orange-a10: color(display-p3 1 0.3176 0.149 / 0.837);
      --orange-a11: color(display-p3 1 0.6275 0.5137 / 0.937);
      --orange-a12: color(display-p3 1 0.8549 0.8078 / 0.971);
    }
  }
}