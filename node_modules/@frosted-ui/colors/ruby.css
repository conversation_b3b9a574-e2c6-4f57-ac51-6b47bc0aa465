:root, .light, .light-theme {
  --ruby-1: #fffcfd;
  --ruby-2: #fff7f8;
  --ruby-3: #feeaed;
  --ruby-4: #ffdce1;
  --ruby-5: #ffced6;
  --ruby-6: #f8bfc8;
  --ruby-7: #efacb8;
  --ruby-8: #e592a3;
  --ruby-9: #e54666;
  --ruby-10: #dc3b5d;
  --ruby-11: #ca244d;
  --ruby-12: #64172b;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --ruby-1: color(display-p3 0.998 0.989 0.992);
      --ruby-2: color(display-p3 0.995 0.971 0.974);
      --ruby-3: color(display-p3 0.983 0.92 0.928);
      --ruby-4: color(display-p3 0.987 0.869 0.885);
      --ruby-5: color(display-p3 0.968 0.817 0.839);
      --ruby-6: color(display-p3 0.937 0.758 0.786);
      --ruby-7: color(display-p3 0.897 0.685 0.721);
      --ruby-8: color(display-p3 0.851 0.588 0.639);
      --ruby-9: color(display-p3 0.83 0.323 0.408);
      --ruby-10: color(display-p3 0.795 0.286 0.375);
      --ruby-11: color(display-p3 0.728 0.211 0.311);
      --ruby-12: color(display-p3 0.36 0.115 0.171);
    }
  }
}