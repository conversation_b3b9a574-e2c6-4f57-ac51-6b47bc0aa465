.dark, .dark-theme {
  --slate-a1: #00000000;
  --slate-a2: #d8f4f609;
  --slate-a3: #ddeaf814;
  --slate-a4: #d3edf81d;
  --slate-a5: #d9edfe25;
  --slate-a6: #d6ebfd30;
  --slate-a7: #d9edff40;
  --slate-a8: #d9edff5d;
  --slate-a9: #dfebfd6d;
  --slate-a10: #e5edfd7b;
  --slate-a11: #f1f7feb5;
  --slate-a12: #fcfdffef;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --slate-a1: color(display-p3 0 0 0 / 0);
      --slate-a2: color(display-p3 0.875 0.992 1 / 0.034);
      --slate-a3: color(display-p3 0.882 0.933 0.992 / 0.077);
      --slate-a4: color(display-p3 0.882 0.953 0.996 / 0.111);
      --slate-a5: color(display-p3 0.878 0.929 0.996 / 0.145);
      --slate-a6: color(display-p3 0.882 0.949 0.996 / 0.183);
      --slate-a7: color(display-p3 0.882 0.929 1 / 0.246);
      --slate-a8: color(display-p3 0.871 0.937 1 / 0.361);
      --slate-a9: color(display-p3 0.898 0.937 1 / 0.42);
      --slate-a10: color(display-p3 0.918 0.945 1 / 0.475);
      --slate-a11: color(display-p3 0.949 0.969 0.996 / 0.708);
      --slate-a12: color(display-p3 0.988 0.992 1 / 0.937);
    }
  }
}