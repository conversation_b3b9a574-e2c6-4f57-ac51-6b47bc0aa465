export declare const gray: {
    gray1: string;
    gray2: string;
    gray3: string;
    gray4: string;
    gray5: string;
    gray6: string;
    gray7: string;
    gray8: string;
    gray9: string;
    gray10: string;
    gray11: string;
    gray12: string;
};
export declare const grayA: {
    grayA1: string;
    grayA2: string;
    grayA3: string;
    grayA4: string;
    grayA5: string;
    grayA6: string;
    grayA7: string;
    grayA8: string;
    grayA9: string;
    grayA10: string;
    grayA11: string;
    grayA12: string;
};
export declare const grayP3: {
    gray1: string;
    gray2: string;
    gray3: string;
    gray4: string;
    gray5: string;
    gray6: string;
    gray7: string;
    gray8: string;
    gray9: string;
    gray10: string;
    gray11: string;
    gray12: string;
};
export declare const grayP3A: {
    grayA1: string;
    grayA2: string;
    grayA3: string;
    grayA4: string;
    grayA5: string;
    grayA6: string;
    grayA7: string;
    grayA8: string;
    grayA9: string;
    grayA10: string;
    grayA11: string;
    grayA12: string;
};
export declare const mauve: {
    mauve1: string;
    mauve2: string;
    mauve3: string;
    mauve4: string;
    mauve5: string;
    mauve6: string;
    mauve7: string;
    mauve8: string;
    mauve9: string;
    mauve10: string;
    mauve11: string;
    mauve12: string;
};
export declare const mauveA: {
    mauveA1: string;
    mauveA2: string;
    mauveA3: string;
    mauveA4: string;
    mauveA5: string;
    mauveA6: string;
    mauveA7: string;
    mauveA8: string;
    mauveA9: string;
    mauveA10: string;
    mauveA11: string;
    mauveA12: string;
};
export declare const mauveP3: {
    mauve1: string;
    mauve2: string;
    mauve3: string;
    mauve4: string;
    mauve5: string;
    mauve6: string;
    mauve7: string;
    mauve8: string;
    mauve9: string;
    mauve10: string;
    mauve11: string;
    mauve12: string;
};
export declare const mauveP3A: {
    mauveA1: string;
    mauveA2: string;
    mauveA3: string;
    mauveA4: string;
    mauveA5: string;
    mauveA6: string;
    mauveA7: string;
    mauveA8: string;
    mauveA9: string;
    mauveA10: string;
    mauveA11: string;
    mauveA12: string;
};
export declare const slate: {
    slate1: string;
    slate2: string;
    slate3: string;
    slate4: string;
    slate5: string;
    slate6: string;
    slate7: string;
    slate8: string;
    slate9: string;
    slate10: string;
    slate11: string;
    slate12: string;
};
export declare const slateA: {
    slateA1: string;
    slateA2: string;
    slateA3: string;
    slateA4: string;
    slateA5: string;
    slateA6: string;
    slateA7: string;
    slateA8: string;
    slateA9: string;
    slateA10: string;
    slateA11: string;
    slateA12: string;
};
export declare const slateP3: {
    slate1: string;
    slate2: string;
    slate3: string;
    slate4: string;
    slate5: string;
    slate6: string;
    slate7: string;
    slate8: string;
    slate9: string;
    slate10: string;
    slate11: string;
    slate12: string;
};
export declare const slateP3A: {
    slateA1: string;
    slateA2: string;
    slateA3: string;
    slateA4: string;
    slateA5: string;
    slateA6: string;
    slateA7: string;
    slateA8: string;
    slateA9: string;
    slateA10: string;
    slateA11: string;
    slateA12: string;
};
export declare const sage: {
    sage1: string;
    sage2: string;
    sage3: string;
    sage4: string;
    sage5: string;
    sage6: string;
    sage7: string;
    sage8: string;
    sage9: string;
    sage10: string;
    sage11: string;
    sage12: string;
};
export declare const sageA: {
    sageA1: string;
    sageA2: string;
    sageA3: string;
    sageA4: string;
    sageA5: string;
    sageA6: string;
    sageA7: string;
    sageA8: string;
    sageA9: string;
    sageA10: string;
    sageA11: string;
    sageA12: string;
};
export declare const sageP3: {
    sage1: string;
    sage2: string;
    sage3: string;
    sage4: string;
    sage5: string;
    sage6: string;
    sage7: string;
    sage8: string;
    sage9: string;
    sage10: string;
    sage11: string;
    sage12: string;
};
export declare const sageP3A: {
    sageA1: string;
    sageA2: string;
    sageA3: string;
    sageA4: string;
    sageA5: string;
    sageA6: string;
    sageA7: string;
    sageA8: string;
    sageA9: string;
    sageA10: string;
    sageA11: string;
    sageA12: string;
};
export declare const olive: {
    olive1: string;
    olive2: string;
    olive3: string;
    olive4: string;
    olive5: string;
    olive6: string;
    olive7: string;
    olive8: string;
    olive9: string;
    olive10: string;
    olive11: string;
    olive12: string;
};
export declare const oliveA: {
    oliveA1: string;
    oliveA2: string;
    oliveA3: string;
    oliveA4: string;
    oliveA5: string;
    oliveA6: string;
    oliveA7: string;
    oliveA8: string;
    oliveA9: string;
    oliveA10: string;
    oliveA11: string;
    oliveA12: string;
};
export declare const oliveP3: {
    olive1: string;
    olive2: string;
    olive3: string;
    olive4: string;
    olive5: string;
    olive6: string;
    olive7: string;
    olive8: string;
    olive9: string;
    olive10: string;
    olive11: string;
    olive12: string;
};
export declare const oliveP3A: {
    oliveA1: string;
    oliveA2: string;
    oliveA3: string;
    oliveA4: string;
    oliveA5: string;
    oliveA6: string;
    oliveA7: string;
    oliveA8: string;
    oliveA9: string;
    oliveA10: string;
    oliveA11: string;
    oliveA12: string;
};
export declare const sand: {
    sand1: string;
    sand2: string;
    sand3: string;
    sand4: string;
    sand5: string;
    sand6: string;
    sand7: string;
    sand8: string;
    sand9: string;
    sand10: string;
    sand11: string;
    sand12: string;
};
export declare const sandA: {
    sandA1: string;
    sandA2: string;
    sandA3: string;
    sandA4: string;
    sandA5: string;
    sandA6: string;
    sandA7: string;
    sandA8: string;
    sandA9: string;
    sandA10: string;
    sandA11: string;
    sandA12: string;
};
export declare const sandP3: {
    sand1: string;
    sand2: string;
    sand3: string;
    sand4: string;
    sand5: string;
    sand6: string;
    sand7: string;
    sand8: string;
    sand9: string;
    sand10: string;
    sand11: string;
    sand12: string;
};
export declare const sandP3A: {
    sandA1: string;
    sandA2: string;
    sandA3: string;
    sandA4: string;
    sandA5: string;
    sandA6: string;
    sandA7: string;
    sandA8: string;
    sandA9: string;
    sandA10: string;
    sandA11: string;
    sandA12: string;
};
export declare const tomato: {
    tomato1: string;
    tomato2: string;
    tomato3: string;
    tomato4: string;
    tomato5: string;
    tomato6: string;
    tomato7: string;
    tomato8: string;
    tomato9: string;
    tomato10: string;
    tomato11: string;
    tomato12: string;
};
export declare const tomatoA: {
    tomatoA1: string;
    tomatoA2: string;
    tomatoA3: string;
    tomatoA4: string;
    tomatoA5: string;
    tomatoA6: string;
    tomatoA7: string;
    tomatoA8: string;
    tomatoA9: string;
    tomatoA10: string;
    tomatoA11: string;
    tomatoA12: string;
};
export declare const tomatoP3: {
    tomato1: string;
    tomato2: string;
    tomato3: string;
    tomato4: string;
    tomato5: string;
    tomato6: string;
    tomato7: string;
    tomato8: string;
    tomato9: string;
    tomato10: string;
    tomato11: string;
    tomato12: string;
};
export declare const tomatoP3A: {
    tomatoA1: string;
    tomatoA2: string;
    tomatoA3: string;
    tomatoA4: string;
    tomatoA5: string;
    tomatoA6: string;
    tomatoA7: string;
    tomatoA8: string;
    tomatoA9: string;
    tomatoA10: string;
    tomatoA11: string;
    tomatoA12: string;
};
export declare const red: {
    red1: string;
    red2: string;
    red3: string;
    red4: string;
    red5: string;
    red6: string;
    red7: string;
    red8: string;
    red9: string;
    red10: string;
    red11: string;
    red12: string;
};
export declare const redA: {
    redA1: string;
    redA2: string;
    redA3: string;
    redA4: string;
    redA5: string;
    redA6: string;
    redA7: string;
    redA8: string;
    redA9: string;
    redA10: string;
    redA11: string;
    redA12: string;
};
export declare const redP3: {
    red1: string;
    red2: string;
    red3: string;
    red4: string;
    red5: string;
    red6: string;
    red7: string;
    red8: string;
    red9: string;
    red10: string;
    red11: string;
    red12: string;
};
export declare const redP3A: {
    redA1: string;
    redA2: string;
    redA3: string;
    redA4: string;
    redA5: string;
    redA6: string;
    redA7: string;
    redA8: string;
    redA9: string;
    redA10: string;
    redA11: string;
    redA12: string;
};
export declare const ruby: {
    ruby1: string;
    ruby2: string;
    ruby3: string;
    ruby4: string;
    ruby5: string;
    ruby6: string;
    ruby7: string;
    ruby8: string;
    ruby9: string;
    ruby10: string;
    ruby11: string;
    ruby12: string;
};
export declare const rubyA: {
    rubyA1: string;
    rubyA2: string;
    rubyA3: string;
    rubyA4: string;
    rubyA5: string;
    rubyA6: string;
    rubyA7: string;
    rubyA8: string;
    rubyA9: string;
    rubyA10: string;
    rubyA11: string;
    rubyA12: string;
};
export declare const rubyP3: {
    ruby1: string;
    ruby2: string;
    ruby3: string;
    ruby4: string;
    ruby5: string;
    ruby6: string;
    ruby7: string;
    ruby8: string;
    ruby9: string;
    ruby10: string;
    ruby11: string;
    ruby12: string;
};
export declare const rubyP3A: {
    rubyA1: string;
    rubyA2: string;
    rubyA3: string;
    rubyA4: string;
    rubyA5: string;
    rubyA6: string;
    rubyA7: string;
    rubyA8: string;
    rubyA9: string;
    rubyA10: string;
    rubyA11: string;
    rubyA12: string;
};
export declare const crimson: {
    crimson1: string;
    crimson2: string;
    crimson3: string;
    crimson4: string;
    crimson5: string;
    crimson6: string;
    crimson7: string;
    crimson8: string;
    crimson9: string;
    crimson10: string;
    crimson11: string;
    crimson12: string;
};
export declare const crimsonA: {
    crimsonA1: string;
    crimsonA2: string;
    crimsonA3: string;
    crimsonA4: string;
    crimsonA5: string;
    crimsonA6: string;
    crimsonA7: string;
    crimsonA8: string;
    crimsonA9: string;
    crimsonA10: string;
    crimsonA11: string;
    crimsonA12: string;
};
export declare const crimsonP3: {
    crimson1: string;
    crimson2: string;
    crimson3: string;
    crimson4: string;
    crimson5: string;
    crimson6: string;
    crimson7: string;
    crimson8: string;
    crimson9: string;
    crimson10: string;
    crimson11: string;
    crimson12: string;
};
export declare const crimsonP3A: {
    crimsonA1: string;
    crimsonA2: string;
    crimsonA3: string;
    crimsonA4: string;
    crimsonA5: string;
    crimsonA6: string;
    crimsonA7: string;
    crimsonA8: string;
    crimsonA9: string;
    crimsonA10: string;
    crimsonA11: string;
    crimsonA12: string;
};
export declare const pink: {
    pink1: string;
    pink2: string;
    pink3: string;
    pink4: string;
    pink5: string;
    pink6: string;
    pink7: string;
    pink8: string;
    pink9: string;
    pink10: string;
    pink11: string;
    pink12: string;
};
export declare const pinkA: {
    pinkA1: string;
    pinkA2: string;
    pinkA3: string;
    pinkA4: string;
    pinkA5: string;
    pinkA6: string;
    pinkA7: string;
    pinkA8: string;
    pinkA9: string;
    pinkA10: string;
    pinkA11: string;
    pinkA12: string;
};
export declare const pinkP3: {
    pink1: string;
    pink2: string;
    pink3: string;
    pink4: string;
    pink5: string;
    pink6: string;
    pink7: string;
    pink8: string;
    pink9: string;
    pink10: string;
    pink11: string;
    pink12: string;
};
export declare const pinkP3A: {
    pinkA1: string;
    pinkA2: string;
    pinkA3: string;
    pinkA4: string;
    pinkA5: string;
    pinkA6: string;
    pinkA7: string;
    pinkA8: string;
    pinkA9: string;
    pinkA10: string;
    pinkA11: string;
    pinkA12: string;
};
export declare const plum: {
    plum1: string;
    plum2: string;
    plum3: string;
    plum4: string;
    plum5: string;
    plum6: string;
    plum7: string;
    plum8: string;
    plum9: string;
    plum10: string;
    plum11: string;
    plum12: string;
};
export declare const plumA: {
    plumA1: string;
    plumA2: string;
    plumA3: string;
    plumA4: string;
    plumA5: string;
    plumA6: string;
    plumA7: string;
    plumA8: string;
    plumA9: string;
    plumA10: string;
    plumA11: string;
    plumA12: string;
};
export declare const plumP3: {
    plum1: string;
    plum2: string;
    plum3: string;
    plum4: string;
    plum5: string;
    plum6: string;
    plum7: string;
    plum8: string;
    plum9: string;
    plum10: string;
    plum11: string;
    plum12: string;
};
export declare const plumP3A: {
    plumA1: string;
    plumA2: string;
    plumA3: string;
    plumA4: string;
    plumA5: string;
    plumA6: string;
    plumA7: string;
    plumA8: string;
    plumA9: string;
    plumA10: string;
    plumA11: string;
    plumA12: string;
};
export declare const purple: {
    purple1: string;
    purple2: string;
    purple3: string;
    purple4: string;
    purple5: string;
    purple6: string;
    purple7: string;
    purple8: string;
    purple9: string;
    purple10: string;
    purple11: string;
    purple12: string;
};
export declare const purpleA: {
    purpleA1: string;
    purpleA2: string;
    purpleA3: string;
    purpleA4: string;
    purpleA5: string;
    purpleA6: string;
    purpleA7: string;
    purpleA8: string;
    purpleA9: string;
    purpleA10: string;
    purpleA11: string;
    purpleA12: string;
};
export declare const purpleP3: {
    purple1: string;
    purple2: string;
    purple3: string;
    purple4: string;
    purple5: string;
    purple6: string;
    purple7: string;
    purple8: string;
    purple9: string;
    purple10: string;
    purple11: string;
    purple12: string;
};
export declare const purpleP3A: {
    purpleA1: string;
    purpleA2: string;
    purpleA3: string;
    purpleA4: string;
    purpleA5: string;
    purpleA6: string;
    purpleA7: string;
    purpleA8: string;
    purpleA9: string;
    purpleA10: string;
    purpleA11: string;
    purpleA12: string;
};
export declare const violet: {
    violet1: string;
    violet2: string;
    violet3: string;
    violet4: string;
    violet5: string;
    violet6: string;
    violet7: string;
    violet8: string;
    violet9: string;
    violet10: string;
    violet11: string;
    violet12: string;
};
export declare const violetA: {
    violetA1: string;
    violetA2: string;
    violetA3: string;
    violetA4: string;
    violetA5: string;
    violetA6: string;
    violetA7: string;
    violetA8: string;
    violetA9: string;
    violetA10: string;
    violetA11: string;
    violetA12: string;
};
export declare const violetP3: {
    violet1: string;
    violet2: string;
    violet3: string;
    violet4: string;
    violet5: string;
    violet6: string;
    violet7: string;
    violet8: string;
    violet9: string;
    violet10: string;
    violet11: string;
    violet12: string;
};
export declare const violetP3A: {
    violetA1: string;
    violetA2: string;
    violetA3: string;
    violetA4: string;
    violetA5: string;
    violetA6: string;
    violetA7: string;
    violetA8: string;
    violetA9: string;
    violetA10: string;
    violetA11: string;
    violetA12: string;
};
export declare const iris: {
    iris1: string;
    iris2: string;
    iris3: string;
    iris4: string;
    iris5: string;
    iris6: string;
    iris7: string;
    iris8: string;
    iris9: string;
    iris10: string;
    iris11: string;
    iris12: string;
};
export declare const irisA: {
    irisA1: string;
    irisA2: string;
    irisA3: string;
    irisA4: string;
    irisA5: string;
    irisA6: string;
    irisA7: string;
    irisA8: string;
    irisA9: string;
    irisA10: string;
    irisA11: string;
    irisA12: string;
};
export declare const irisP3: {
    iris1: string;
    iris2: string;
    iris3: string;
    iris4: string;
    iris5: string;
    iris6: string;
    iris7: string;
    iris8: string;
    iris9: string;
    iris10: string;
    iris11: string;
    iris12: string;
};
export declare const irisP3A: {
    irisA1: string;
    irisA2: string;
    irisA3: string;
    irisA4: string;
    irisA5: string;
    irisA6: string;
    irisA7: string;
    irisA8: string;
    irisA9: string;
    irisA10: string;
    irisA11: string;
    irisA12: string;
};
export declare const cyan: {
    cyan1: string;
    cyan2: string;
    cyan3: string;
    cyan4: string;
    cyan5: string;
    cyan6: string;
    cyan7: string;
    cyan8: string;
    cyan9: string;
    cyan10: string;
    cyan11: string;
    cyan12: string;
};
export declare const cyanA: {
    cyanA1: string;
    cyanA2: string;
    cyanA3: string;
    cyanA4: string;
    cyanA5: string;
    cyanA6: string;
    cyanA7: string;
    cyanA8: string;
    cyanA9: string;
    cyanA10: string;
    cyanA11: string;
    cyanA12: string;
};
export declare const cyanP3: {
    cyan1: string;
    cyan2: string;
    cyan3: string;
    cyan4: string;
    cyan5: string;
    cyan6: string;
    cyan7: string;
    cyan8: string;
    cyan9: string;
    cyan10: string;
    cyan11: string;
    cyan12: string;
};
export declare const cyanP3A: {
    cyanA1: string;
    cyanA2: string;
    cyanA3: string;
    cyanA4: string;
    cyanA5: string;
    cyanA6: string;
    cyanA7: string;
    cyanA8: string;
    cyanA9: string;
    cyanA10: string;
    cyanA11: string;
    cyanA12: string;
};
export declare const teal: {
    teal1: string;
    teal2: string;
    teal3: string;
    teal4: string;
    teal5: string;
    teal6: string;
    teal7: string;
    teal8: string;
    teal9: string;
    teal10: string;
    teal11: string;
    teal12: string;
};
export declare const tealA: {
    tealA1: string;
    tealA2: string;
    tealA3: string;
    tealA4: string;
    tealA5: string;
    tealA6: string;
    tealA7: string;
    tealA8: string;
    tealA9: string;
    tealA10: string;
    tealA11: string;
    tealA12: string;
};
export declare const tealP3: {
    teal1: string;
    teal2: string;
    teal3: string;
    teal4: string;
    teal5: string;
    teal6: string;
    teal7: string;
    teal8: string;
    teal9: string;
    teal10: string;
    teal11: string;
    teal12: string;
};
export declare const tealP3A: {
    tealA1: string;
    tealA2: string;
    tealA3: string;
    tealA4: string;
    tealA5: string;
    tealA6: string;
    tealA7: string;
    tealA8: string;
    tealA9: string;
    tealA10: string;
    tealA11: string;
    tealA12: string;
};
export declare const jade: {
    jade1: string;
    jade2: string;
    jade3: string;
    jade4: string;
    jade5: string;
    jade6: string;
    jade7: string;
    jade8: string;
    jade9: string;
    jade10: string;
    jade11: string;
    jade12: string;
};
export declare const jadeA: {
    jadeA1: string;
    jadeA2: string;
    jadeA3: string;
    jadeA4: string;
    jadeA5: string;
    jadeA6: string;
    jadeA7: string;
    jadeA8: string;
    jadeA9: string;
    jadeA10: string;
    jadeA11: string;
    jadeA12: string;
};
export declare const jadeP3: {
    jade1: string;
    jade2: string;
    jade3: string;
    jade4: string;
    jade5: string;
    jade6: string;
    jade7: string;
    jade8: string;
    jade9: string;
    jade10: string;
    jade11: string;
    jade12: string;
};
export declare const jadeP3A: {
    jadeA1: string;
    jadeA2: string;
    jadeA3: string;
    jadeA4: string;
    jadeA5: string;
    jadeA6: string;
    jadeA7: string;
    jadeA8: string;
    jadeA9: string;
    jadeA10: string;
    jadeA11: string;
    jadeA12: string;
};
export declare const green: {
    green1: string;
    green2: string;
    green3: string;
    green4: string;
    green5: string;
    green6: string;
    green7: string;
    green8: string;
    green9: string;
    green10: string;
    green11: string;
    green12: string;
};
export declare const greenA: {
    greenA1: string;
    greenA2: string;
    greenA3: string;
    greenA4: string;
    greenA5: string;
    greenA6: string;
    greenA7: string;
    greenA8: string;
    greenA9: string;
    greenA10: string;
    greenA11: string;
    greenA12: string;
};
export declare const greenP3: {
    green1: string;
    green2: string;
    green3: string;
    green4: string;
    green5: string;
    green6: string;
    green7: string;
    green8: string;
    green9: string;
    green10: string;
    green11: string;
    green12: string;
};
export declare const greenP3A: {
    greenA1: string;
    greenA2: string;
    greenA3: string;
    greenA4: string;
    greenA5: string;
    greenA6: string;
    greenA7: string;
    greenA8: string;
    greenA9: string;
    greenA10: string;
    greenA11: string;
    greenA12: string;
};
export declare const grass: {
    grass1: string;
    grass2: string;
    grass3: string;
    grass4: string;
    grass5: string;
    grass6: string;
    grass7: string;
    grass8: string;
    grass9: string;
    grass10: string;
    grass11: string;
    grass12: string;
};
export declare const grassA: {
    grassA1: string;
    grassA2: string;
    grassA3: string;
    grassA4: string;
    grassA5: string;
    grassA6: string;
    grassA7: string;
    grassA8: string;
    grassA9: string;
    grassA10: string;
    grassA11: string;
    grassA12: string;
};
export declare const grassP3: {
    grass1: string;
    grass2: string;
    grass3: string;
    grass4: string;
    grass5: string;
    grass6: string;
    grass7: string;
    grass8: string;
    grass9: string;
    grass10: string;
    grass11: string;
    grass12: string;
};
export declare const grassP3A: {
    grassA1: string;
    grassA2: string;
    grassA3: string;
    grassA4: string;
    grassA5: string;
    grassA6: string;
    grassA7: string;
    grassA8: string;
    grassA9: string;
    grassA10: string;
    grassA11: string;
    grassA12: string;
};
export declare const brown: {
    brown1: string;
    brown2: string;
    brown3: string;
    brown4: string;
    brown5: string;
    brown6: string;
    brown7: string;
    brown8: string;
    brown9: string;
    brown10: string;
    brown11: string;
    brown12: string;
};
export declare const brownA: {
    brownA1: string;
    brownA2: string;
    brownA3: string;
    brownA4: string;
    brownA5: string;
    brownA6: string;
    brownA7: string;
    brownA8: string;
    brownA9: string;
    brownA10: string;
    brownA11: string;
    brownA12: string;
};
export declare const brownP3: {
    brown1: string;
    brown2: string;
    brown3: string;
    brown4: string;
    brown5: string;
    brown6: string;
    brown7: string;
    brown8: string;
    brown9: string;
    brown10: string;
    brown11: string;
    brown12: string;
};
export declare const brownP3A: {
    brownA1: string;
    brownA2: string;
    brownA3: string;
    brownA4: string;
    brownA5: string;
    brownA6: string;
    brownA7: string;
    brownA8: string;
    brownA9: string;
    brownA10: string;
    brownA11: string;
    brownA12: string;
};
export declare const bronze: {
    bronze1: string;
    bronze2: string;
    bronze3: string;
    bronze4: string;
    bronze5: string;
    bronze6: string;
    bronze7: string;
    bronze8: string;
    bronze9: string;
    bronze10: string;
    bronze11: string;
    bronze12: string;
};
export declare const bronzeA: {
    bronzeA1: string;
    bronzeA2: string;
    bronzeA3: string;
    bronzeA4: string;
    bronzeA5: string;
    bronzeA6: string;
    bronzeA7: string;
    bronzeA8: string;
    bronzeA9: string;
    bronzeA10: string;
    bronzeA11: string;
    bronzeA12: string;
};
export declare const bronzeP3: {
    bronze1: string;
    bronze2: string;
    bronze3: string;
    bronze4: string;
    bronze5: string;
    bronze6: string;
    bronze7: string;
    bronze8: string;
    bronze9: string;
    bronze10: string;
    bronze11: string;
    bronze12: string;
};
export declare const bronzeP3A: {
    bronzeA1: string;
    bronzeA2: string;
    bronzeA3: string;
    bronzeA4: string;
    bronzeA5: string;
    bronzeA6: string;
    bronzeA7: string;
    bronzeA8: string;
    bronzeA9: string;
    bronzeA10: string;
    bronzeA11: string;
    bronzeA12: string;
};
export declare const gold: {
    gold1: string;
    gold2: string;
    gold3: string;
    gold4: string;
    gold5: string;
    gold6: string;
    gold7: string;
    gold8: string;
    gold9: string;
    gold10: string;
    gold11: string;
    gold12: string;
};
export declare const goldA: {
    goldA1: string;
    goldA2: string;
    goldA3: string;
    goldA4: string;
    goldA5: string;
    goldA6: string;
    goldA7: string;
    goldA8: string;
    goldA9: string;
    goldA10: string;
    goldA11: string;
    goldA12: string;
};
export declare const goldP3: {
    gold1: string;
    gold2: string;
    gold3: string;
    gold4: string;
    gold5: string;
    gold6: string;
    gold7: string;
    gold8: string;
    gold9: string;
    gold10: string;
    gold11: string;
    gold12: string;
};
export declare const goldP3A: {
    goldA1: string;
    goldA2: string;
    goldA3: string;
    goldA4: string;
    goldA5: string;
    goldA6: string;
    goldA7: string;
    goldA8: string;
    goldA9: string;
    goldA10: string;
    goldA11: string;
    goldA12: string;
};
export declare const sky: {
    sky1: string;
    sky2: string;
    sky3: string;
    sky4: string;
    sky5: string;
    sky6: string;
    sky7: string;
    sky8: string;
    sky9: string;
    sky10: string;
    sky11: string;
    sky12: string;
};
export declare const skyA: {
    skyA1: string;
    skyA2: string;
    skyA3: string;
    skyA4: string;
    skyA5: string;
    skyA6: string;
    skyA7: string;
    skyA8: string;
    skyA9: string;
    skyA10: string;
    skyA11: string;
    skyA12: string;
};
export declare const skyP3: {
    sky1: string;
    sky2: string;
    sky3: string;
    sky4: string;
    sky5: string;
    sky6: string;
    sky7: string;
    sky8: string;
    sky9: string;
    sky10: string;
    sky11: string;
    sky12: string;
};
export declare const skyP3A: {
    skyA1: string;
    skyA2: string;
    skyA3: string;
    skyA4: string;
    skyA5: string;
    skyA6: string;
    skyA7: string;
    skyA8: string;
    skyA9: string;
    skyA10: string;
    skyA11: string;
    skyA12: string;
};
export declare const mint: {
    mint1: string;
    mint2: string;
    mint3: string;
    mint4: string;
    mint5: string;
    mint6: string;
    mint7: string;
    mint8: string;
    mint9: string;
    mint10: string;
    mint11: string;
    mint12: string;
};
export declare const mintA: {
    mintA1: string;
    mintA2: string;
    mintA3: string;
    mintA4: string;
    mintA5: string;
    mintA6: string;
    mintA7: string;
    mintA8: string;
    mintA9: string;
    mintA10: string;
    mintA11: string;
    mintA12: string;
};
export declare const mintP3: {
    mint1: string;
    mint2: string;
    mint3: string;
    mint4: string;
    mint5: string;
    mint6: string;
    mint7: string;
    mint8: string;
    mint9: string;
    mint10: string;
    mint11: string;
    mint12: string;
};
export declare const mintP3A: {
    mintA1: string;
    mintA2: string;
    mintA3: string;
    mintA4: string;
    mintA5: string;
    mintA6: string;
    mintA7: string;
    mintA8: string;
    mintA9: string;
    mintA10: string;
    mintA11: string;
    mintA12: string;
};
export declare const yellow: {
    yellow1: string;
    yellow2: string;
    yellow3: string;
    yellow4: string;
    yellow5: string;
    yellow6: string;
    yellow7: string;
    yellow8: string;
    yellow9: string;
    yellow10: string;
    yellow11: string;
    yellow12: string;
};
export declare const yellowA: {
    yellowA1: string;
    yellowA2: string;
    yellowA3: string;
    yellowA4: string;
    yellowA5: string;
    yellowA6: string;
    yellowA7: string;
    yellowA8: string;
    yellowA9: string;
    yellowA10: string;
    yellowA11: string;
    yellowA12: string;
};
export declare const yellowP3: {
    yellow1: string;
    yellow2: string;
    yellow3: string;
    yellow4: string;
    yellow5: string;
    yellow6: string;
    yellow7: string;
    yellow8: string;
    yellow9: string;
    yellow10: string;
    yellow11: string;
    yellow12: string;
};
export declare const yellowP3A: {
    yellowA1: string;
    yellowA2: string;
    yellowA3: string;
    yellowA4: string;
    yellowA5: string;
    yellowA6: string;
    yellowA7: string;
    yellowA8: string;
    yellowA9: string;
    yellowA10: string;
    yellowA11: string;
    yellowA12: string;
};
export declare const amber: {
    amber1: string;
    amber2: string;
    amber3: string;
    amber4: string;
    amber5: string;
    amber6: string;
    amber7: string;
    amber8: string;
    amber9: string;
    amber10: string;
    amber11: string;
    amber12: string;
};
export declare const amberA: {
    amberA1: string;
    amberA2: string;
    amberA3: string;
    amberA4: string;
    amberA5: string;
    amberA6: string;
    amberA7: string;
    amberA8: string;
    amberA9: string;
    amberA10: string;
    amberA11: string;
    amberA12: string;
};
export declare const amberP3: {
    amber1: string;
    amber2: string;
    amber3: string;
    amber4: string;
    amber5: string;
    amber6: string;
    amber7: string;
    amber8: string;
    amber9: string;
    amber10: string;
    amber11: string;
    amber12: string;
};
export declare const amberP3A: {
    amberA1: string;
    amberA2: string;
    amberA3: string;
    amberA4: string;
    amberA5: string;
    amberA6: string;
    amberA7: string;
    amberA8: string;
    amberA9: string;
    amberA10: string;
    amberA11: string;
    amberA12: string;
};
export declare const blue: {
    blue1: string;
    blue2: string;
    blue3: string;
    blue4: string;
    blue5: string;
    blue6: string;
    blue7: string;
    blue8: string;
    blue9: string;
    blue10: string;
    blue11: string;
    blue12: string;
};
export declare const blueA: {
    blueA1: string;
    blueA2: string;
    blueA3: string;
    blueA4: string;
    blueA5: string;
    blueA6: string;
    blueA7: string;
    blueA8: string;
    blueA9: string;
    blueA10: string;
    blueA11: string;
    blueA12: string;
};
export declare const blueP3: {
    blue1: string;
    blue2: string;
    blue3: string;
    blue4: string;
    blue5: string;
    blue6: string;
    blue7: string;
    blue8: string;
    blue9: string;
    blue10: string;
    blue11: string;
    blue12: string;
};
export declare const blueP3A: {
    blueA1: string;
    blueA2: string;
    blueA3: string;
    blueA4: string;
    blueA5: string;
    blueA6: string;
    blueA7: string;
    blueA8: string;
    blueA9: string;
    blueA10: string;
    blueA11: string;
    blueA12: string;
};
export declare const orange: {
    orange1: string;
    orange2: string;
    orange3: string;
    orange4: string;
    orange5: string;
    orange6: string;
    orange7: string;
    orange8: string;
    orange9: string;
    orange10: string;
    orange11: string;
    orange12: string;
};
export declare const orangeA: {
    orangeA1: string;
    orangeA2: string;
    orangeA3: string;
    orangeA4: string;
    orangeA5: string;
    orangeA6: string;
    orangeA7: string;
    orangeA8: string;
    orangeA9: string;
    orangeA10: string;
    orangeA11: string;
    orangeA12: string;
};
export declare const orangeP3: {
    orange1: string;
    orange2: string;
    orange3: string;
    orange4: string;
    orange5: string;
    orange6: string;
    orange7: string;
    orange8: string;
    orange9: string;
    orange10: string;
    orange11: string;
    orange12: string;
};
export declare const orangeP3A: {
    orangeA1: string;
    orangeA2: string;
    orangeA3: string;
    orangeA4: string;
    orangeA5: string;
    orangeA6: string;
    orangeA7: string;
    orangeA8: string;
    orangeA9: string;
    orangeA10: string;
    orangeA11: string;
    orangeA12: string;
};
export declare const lemon: {
    lemon1: string;
    lemon2: string;
    lemon3: string;
    lemon4: string;
    lemon5: string;
    lemon6: string;
    lemon7: string;
    lemon8: string;
    lemon9: string;
    lemon10: string;
    lemon11: string;
    lemon12: string;
};
export declare const lemonA: {
    lemonA1: string;
    lemonA2: string;
    lemonA3: string;
    lemonA4: string;
    lemonA5: string;
    lemonA6: string;
    lemonA7: string;
    lemonA8: string;
    lemonA9: string;
    lemonA10: string;
    lemonA11: string;
    lemonA12: string;
};
export declare const lemonP3: {
    lemon1: string;
    lemon2: string;
    lemon3: string;
    lemon4: string;
    lemon5: string;
    lemon6: string;
    lemon7: string;
    lemon8: string;
    lemon9: string;
    lemon10: string;
    lemon11: string;
    lemon12: string;
};
export declare const lemonP3A: {
    lemonA1: string;
    lemonA2: string;
    lemonA3: string;
    lemonA4: string;
    lemonA5: string;
    lemonA6: string;
    lemonA7: string;
    lemonA8: string;
    lemonA9: string;
    lemonA10: string;
    lemonA11: string;
    lemonA12: string;
};
export declare const indigo: {
    indigo1: string;
    indigo2: string;
    indigo3: string;
    indigo4: string;
    indigo5: string;
    indigo6: string;
    indigo7: string;
    indigo8: string;
    indigo9: string;
    indigo10: string;
    indigo11: string;
    indigo12: string;
};
export declare const indigoA: {
    indigoA1: string;
    indigoA2: string;
    indigoA3: string;
    indigoA4: string;
    indigoA5: string;
    indigoA6: string;
    indigoA7: string;
    indigoA8: string;
    indigoA9: string;
    indigoA10: string;
    indigoA11: string;
    indigoA12: string;
};
export declare const indigoP3: {
    indigo1: string;
    indigo2: string;
    indigo3: string;
    indigo4: string;
    indigo5: string;
    indigo6: string;
    indigo7: string;
    indigo8: string;
    indigo9: string;
    indigo10: string;
    indigo11: string;
    indigo12: string;
};
export declare const indigoP3A: {
    indigoA1: string;
    indigoA2: string;
    indigoA3: string;
    indigoA4: string;
    indigoA5: string;
    indigoA6: string;
    indigoA7: string;
    indigoA8: string;
    indigoA9: string;
    indigoA10: string;
    indigoA11: string;
    indigoA12: string;
};
export declare const lime: {
    lime1: string;
    lime2: string;
    lime3: string;
    lime4: string;
    lime5: string;
    lime6: string;
    lime7: string;
    lime8: string;
    lime9: string;
    lime10: string;
    lime11: string;
    lime12: string;
};
export declare const limeA: {
    limeA1: string;
    limeA2: string;
    limeA3: string;
    limeA4: string;
    limeA5: string;
    limeA6: string;
    limeA7: string;
    limeA8: string;
    limeA9: string;
    limeA10: string;
    limeA11: string;
    limeA12: string;
};
export declare const limeP3: {
    lime1: string;
    lime2: string;
    lime3: string;
    lime4: string;
    lime5: string;
    lime6: string;
    lime7: string;
    lime8: string;
    lime9: string;
    lime10: string;
    lime11: string;
    lime12: string;
};
export declare const limeP3A: {
    limeA1: string;
    limeA2: string;
    limeA3: string;
    limeA4: string;
    limeA5: string;
    limeA6: string;
    limeA7: string;
    limeA8: string;
    limeA9: string;
    limeA10: string;
    limeA11: string;
    limeA12: string;
};
export declare const magenta: {
    magenta1: string;
    magenta2: string;
    magenta3: string;
    magenta4: string;
    magenta5: string;
    magenta6: string;
    magenta7: string;
    magenta8: string;
    magenta9: string;
    magenta10: string;
    magenta11: string;
    magenta12: string;
};
export declare const magentaA: {
    magentaA1: string;
    magentaA2: string;
    magentaA3: string;
    magentaA4: string;
    magentaA5: string;
    magentaA6: string;
    magentaA7: string;
    magentaA8: string;
    magentaA9: string;
    magentaA10: string;
    magentaA11: string;
    magentaA12: string;
};
export declare const magentaP3: {
    magenta1: string;
    magenta2: string;
    magenta3: string;
    magenta4: string;
    magenta5: string;
    magenta6: string;
    magenta7: string;
    magenta8: string;
    magenta9: string;
    magenta10: string;
    magenta11: string;
    magenta12: string;
};
export declare const magentaP3A: {
    magentaA1: string;
    magentaA2: string;
    magentaA3: string;
    magentaA4: string;
    magentaA5: string;
    magentaA6: string;
    magentaA7: string;
    magentaA8: string;
    magentaA9: string;
    magentaA10: string;
    magentaA11: string;
    magentaA12: string;
};
