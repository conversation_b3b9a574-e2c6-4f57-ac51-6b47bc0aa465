{"mappings": ";;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;AAKhG,MAAM,mCAAa,SAAS,gFAAgF;AAC5G,MAAM,0CAAoB,SAAS,4CAA4C;AAC/E,MAAM,4CAAsB;AAC5B,MAAM,0CAAoB;AAC1B,MAAM,4CAAsB;AAE5B,SAAS,yCAAmB,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IACjF,OAAO,MACL,KAAK,IAAI,CAAC,OAAQ,CAAA,QAAQ,CAAA,KAC1B,AAAC,CAAA,OAAO,CAAA,IAAK,MACb,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,KAAK,IAAG,IAAK,MAC7B,QAAQ;AACZ;AAEA,SAAS,yCAAmB,QAAkB,EAAE,KAAa,EAAE,EAAU;IACvE,IAAI,OAAO,KAAK,KAAK,CAAC,AAAC,CAAA,KAAM,CAAA,KAAK,KAAI,IAAK,KAAI,IAAK;IACpD,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,AAAC,CAAA,KAAM,CAAA,KAAK,yCAAmB,OAAO,MAAM,GAAG,EAAC,CAAC,IAAK,QAAQ;IACjG,IAAI,MAAM,KAAK,yCAAmB,OAAO,MAAM,OAAO,KAAK;IAE3D,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,UAAU,MAAM,OAAO;AACjD;AAEA,SAAS,iCAAW,IAAY;IAC9B,OAAO,AAAC,CAAA,KAAK,KAAK,IAAG,IAAK,KAAK;AACjC;AASO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,OAAO,yCAAmB,IAAI,EAAE,kCAAY;IAC9C;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,yCAAmB,kCAAY,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IACvE;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,SAAS,KAAK,KAAK,KAAK,GAAG;QAC/B,IAAI,KAAK,KAAK,KAAK,MAAM,iCAAW,KAAK,IAAI,GAC3C;QAGF,OAAO;IACT;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,iCAAW,KAAK,IAAI,IAAI,MAAM;IACvC;IAEA,gBAAwB;QACtB,iBAAiB;QACjB,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;;aAlCA,aAAiC;;AAmCnC;AASO,MAAM,kDAA+B;IAG1C,cAAc,EAAU,EAAgB;QACtC,OAAO,yCAAmB,IAAI,EAAE,yCAAmB;IACrD;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,yCAAmB,yCAAmB,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IAC9E;;QATK,qBACL,aAAiC;;AASnC;AAEA,4CAA4C;AAC5C,MAAM,sCAAgB;AACtB,IAAI;AACJ,IAAI;AAEJ,SAAS,wCAAkB,IAAY;IACrC,OAAO,4CAAsB,+CAAyB,CAAC,OAAO,0CAAoB;AACpF;AAEA,SAAS,0CAAoB,IAAY,EAAE,KAAa;IACtD,IAAI,MAAO,OAAO;IAClB,IAAI,OAAQ,QAAS,KAAM,CAAA,QAAQ,CAAA;IACnC,IAAI,AAAC,CAAA,0CAAoB,CAAC,IAAI,GAAG,IAAG,MAAO,GACzC,OAAO;SAEP,OAAO;AAEX;AAEA,SAAS,yCAAmB,IAAY,EAAE,KAAa;IACrD,IAAI,MAAM,wCAAkB;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IACzB,OAAO,0CAAoB,MAAM;IAEnC,OAAO;AACT;AAEA,SAAS,yCAAmB,IAAY;IACtC,OAAO,+CAAyB,CAAC,OAAO,IAAI,0CAAoB,GAAG,+CAAyB,CAAC,OAAO,0CAAoB;AAC1H;AASO,MAAM,kDAAgC;IAsB3C,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK;QAChB,IAAI,YAAY,wCAAkB;QAClC,IAAI,UAAU,wCAAkB;QAChC,IAAI,OAAO,aAAa,OAAO,SAC7B,OAAO,KAAK,CAAC,cAAc;aACtB;YACL,IAAI,IAAI,4CAAsB;YAC9B,IAAI,IAAI;YACR,IAAI,IAAI;YACR,MAAO,IAAI,EAAG;gBACZ;gBACA,IAAI,OAAO,wCAAkB,KAAK;gBAClC,IAAI,aAAa,yCAAmB;gBACpC,IAAI,MAAM,YAAY;oBACpB,IAAI;oBACJ;gBACF,OAAO,IAAI,IAAI,YAAY;oBACzB,IAAI,cAAc,0CAAoB,GAAG;oBACzC,IAAI;oBACJ,MAAO,IAAI,YAAa;wBACtB,KAAK;wBACL;wBACA,cAAc,0CAAoB,GAAG;oBACvC;oBACA;gBACF;YACF;YAEA,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,GAAG,GAAI,OAAO,yCAAmB,GAAG,KAAK;QACzE;IACF;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,YAAY;QAG3B,OAAO,mCAAa,yCAAmB,KAAK,IAAI,EAAE,KAAK,KAAK,IAAK,CAAA,KAAK,GAAG,GAAG,CAAA;IAC9E;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,eAAe;QAG9B,OAAO,0CAAoB,KAAK,IAAI,EAAE,KAAK,KAAK;IAClD;IAEA,cAAc,IAAqB,EAAU;QAC3C,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,cAAc;QAG7B,OAAO,yCAAmB,KAAK,IAAI;IACrC;IA1EA,aAAc;QACZ,KAAK,SAHP,aAAiC;QAI/B,IAAI,CAAC,4CACH,6CAAuB,IAAI,YAAY,WAAW,IAAI,CAAC,KAAK,sCAAgB,CAAA,IAAK,EAAE,UAAU,CAAC,IAAI,MAAM;QAG1G,IAAI,CAAC,iDAA2B;YAC9B,kDAA4B,IAAI,YAAY,0CAAoB,4CAAsB;YAEtF,IAAI,YAAY;YAChB,IAAK,IAAI,OAAO,2CAAqB,QAAQ,yCAAmB,OAAQ;gBACtE,+CAAyB,CAAC,OAAO,0CAAoB,GAAG;gBACxD,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IACvB,aAAa,0CAAoB,MAAM;YAE3C;QACF;IACF;AA0DF", "sources": ["packages/@internationalized/date/src/calendars/IslamicCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\n\nconst CIVIL_EPOC = 1948440; // CE 622 July 16 Friday (Julian calendar) / CE 622 July 19 (Gregorian calendar)\nconst ASTRONOMICAL_EPOC = 1948439; // CE 622 July 15 Thursday (Julian calendar)\nconst UMALQURA_YEAR_START = 1300;\nconst UMALQURA_YEAR_END = 1600;\nconst UMALQURA_START_DAYS = 460322;\n\nfunction islamicToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return day +\n    Math.ceil(29.5 * (month - 1)) +\n    (year - 1) * 354 +\n    Math.floor((3 + 11 * year) / 30) +\n    epoch - 1;\n}\n\nfunction julianDayToIslamic(calendar: Calendar, epoch: number, jd: number) {\n  let year = Math.floor((30 * (jd - epoch) + 10646) / 10631);\n  let month = Math.min(12, Math.ceil((jd - (29 + islamicToJulianDay(epoch, year, 1, 1))) / 29.5) + 1);\n  let day = jd - islamicToJulianDay(epoch, year, month, 1) + 1;\n\n  return new CalendarDate(calendar, year, month, day);\n}\n\nfunction isLeapYear(year: number): boolean {\n  return (14 + 11 * year) % 30 < 11;\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The civil variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Friday, July 16 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicCivilCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'islamic-civil';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, CIVIL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(CIVIL_EPOC, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let length = 29 + date.month % 2;\n    if (date.month === 12 && isLeapYear(date.year)) {\n      length++;\n    }\n\n    return length;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 355 : 354;\n  }\n\n  getYearsInEra(): number {\n    // 9999 gregorian\n    return 9665;\n  }\n\n  getEras(): string[] {\n    return ['AH'];\n  }\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The tabular variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Thursday, July 15 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicTabularCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-tbla';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, ASTRONOMICAL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(ASTRONOMICAL_EPOC, date.year, date.month, date.day);\n  }\n}\n\n// Generated by scripts/generate-umalqura.js\nconst UMALQURA_DATA = 'qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=';\nlet UMALQURA_MONTHLENGTH: Uint16Array;\nlet UMALQURA_YEAR_START_TABLE: Uint32Array;\n\nfunction umalquraYearStart(year: number): number {\n  return UMALQURA_START_DAYS + UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\nfunction umalquraMonthLength(year: number, month: number): number {\n  let idx = (year - UMALQURA_YEAR_START);\n  let mask = (0x01 << (11 - (month - 1)));\n  if ((UMALQURA_MONTHLENGTH[idx] & mask) === 0) {\n    return 29;\n  } else {\n    return 30;\n  }\n}\n\nfunction umalquraMonthStart(year: number, month: number): number {\n  let day = umalquraYearStart(year);\n  for (let i = 1; i < month; i++) {\n    day += umalquraMonthLength(year, i);\n  }\n  return day;\n}\n\nfunction umalquraYearLength(year: number): number {\n  return UMALQURA_YEAR_START_TABLE[year + 1 - UMALQURA_YEAR_START] - UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The Umalqura variant is primarily used in Saudi Arabia. It is a lunar calendar, based on astronomical\n * calculations that predict the sighting of a crescent moon. Month and year lengths vary between years\n * depending on these calculations.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicUmalquraCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-umalqura';\n\n  constructor() {\n    super();\n    if (!UMALQURA_MONTHLENGTH) {\n      UMALQURA_MONTHLENGTH = new Uint16Array(Uint8Array.from(atob(UMALQURA_DATA), c => c.charCodeAt(0)).buffer);\n    }\n\n    if (!UMALQURA_YEAR_START_TABLE) {\n      UMALQURA_YEAR_START_TABLE = new Uint32Array(UMALQURA_YEAR_END - UMALQURA_YEAR_START + 1);\n\n      let yearStart = 0;\n      for (let year = UMALQURA_YEAR_START; year <= UMALQURA_YEAR_END; year++) {\n        UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START] = yearStart;\n        for (let i = 1; i <= 12; i++) {\n          yearStart += umalquraMonthLength(year, i);\n        }\n      }\n    }\n  }\n\n  fromJulianDay(jd: number): CalendarDate {\n    let days = jd - CIVIL_EPOC;\n    let startDays = umalquraYearStart(UMALQURA_YEAR_START);\n    let endDays = umalquraYearStart(UMALQURA_YEAR_END);\n    if (days < startDays || days > endDays) {\n      return super.fromJulianDay(jd);\n    } else {\n      let y = UMALQURA_YEAR_START - 1;\n      let m = 1;\n      let d = 1;\n      while (d > 0) {\n        y++;\n        d = days - umalquraYearStart(y) + 1;\n        let yearLength = umalquraYearLength(y);\n        if (d === yearLength) {\n          m = 12;\n          break;\n        } else if (d < yearLength) {\n          let monthLength = umalquraMonthLength(y, m);\n          m = 1;\n          while (d > monthLength) {\n            d -= monthLength;\n            m++;\n            monthLength = umalquraMonthLength(y, m);\n          }\n          break;\n        }\n      }\n\n      return new CalendarDate(this, y, m, (days - umalquraMonthStart(y, m) + 1));\n    }\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.toJulianDay(date);\n    }\n\n    return CIVIL_EPOC + umalquraMonthStart(date.year, date.month) + (date.day - 1);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInMonth(date);\n    }\n\n    return umalquraMonthLength(date.year, date.month);\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInYear(date);\n    }\n\n    return umalquraYearLength(date.year);\n  }\n}\n"], "names": [], "version": 3, "file": "IslamicCalendar.module.js.map"}