{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,uFAAuF;AACvF,gGAAgG;;;AAOhG,MAAM,wCAAkB;IAAC;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAG;IAAE;QAAC;QAAM;QAAI;KAAG;IAAE;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAE;CAAC;AACjG,MAAM,sCAAgB;IAAC;QAAC;QAAM;QAAG;KAAG;IAAE;QAAC;QAAM;QAAI;KAAG;IAAE;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAG;CAAC;AAClF,MAAM,oCAAc;IAAC;IAAM;IAAM;IAAM;IAAM;CAAK;AAClD,MAAM,kCAAY;IAAC;IAAS;IAAU;IAAS;IAAU;CAAQ;AAEjE,SAAS,+CAAyB,IAAqB;IACrD,MAAM,MAAM,sCAAgB,SAAS,CAAC,CAAC,CAAC,MAAM,OAAO,IAAI;QACvD,IAAI,KAAK,IAAI,GAAG,MACd,OAAO;QAGT,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,GAAG,OACrC,OAAO;QAGT,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG,GAAG,KAC3D,OAAO;QAGT,OAAO;IACT;IAEA,IAAI,QAAQ,IACV,OAAO,sCAAgB,MAAM,GAAG;IAGlC,IAAI,QAAQ,GACV,OAAO;IAGT,OAAO,MAAM;AACf;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,YAAY,iCAAW,CAAC,gCAAU,OAAO,CAAC,KAAK,GAAG,EAAE;IACxD,IAAI,CAAC,WACH,MAAM,IAAI,MAAM,kBAAkB,KAAK,GAAG;IAG5C,OAAO,IAAI,CAAA,GAAA,yCAAW,EACpB,KAAK,IAAI,GAAG,WACZ,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ;AAOO,MAAM,kDAAyB,CAAA,GAAA,yCAAgB;IAGpD,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK,CAAC,cAAc;QAC/B,IAAI,MAAM,+CAAyB;QAEnC,OAAO,IAAI,CAAA,GAAA,yCAAW,EACpB,IAAI,EACJ,+BAAS,CAAC,IAAI,EACd,KAAK,IAAI,GAAG,iCAAW,CAAC,IAAI,EAC5B,KAAK,KAAK,EACV,KAAK,GAAG;IAEZ;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,gBAAgB,kCAAY;QAChC,IAAI,MAAM,+CAAyB;QAEnC,IAAI,+BAAS,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YAC/B,KAAK,GAAG,GAAG,+BAAS,CAAC,IAAI;YACzB,KAAK,IAAI,GAAG,cAAc,IAAI,GAAG,iCAAW,CAAC,IAAI;QACnD;QAEA,4DAA4D;QAC5D,IAAI,CAAC,aAAa,CAAC;IACrB;IAEA,cAAc,IAA8B,EAAQ;QAClD,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,IAAI,MAAM,mCAAa,CAAC,IAAI;QAC5B,IAAI,OAAO,MAAM;YACf,IAAI,CAAC,SAAS,UAAU,OAAO,GAAG;YAElC,+DAA+D;YAC/D,uDAAuD;YACvD,IAAI,UAAU,UAAU,iCAAW,CAAC,IAAI;YACxC,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,IAAI;YACnD,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK;gBAE1C,IAAI,KAAK,KAAK,KAAK,UACjB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;YAExC;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,GAAG;YAC/B,IAAI,GAAG,YAAY,SAAS,GAAG,qCAAe,CAAC,IAAI;YACnD,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,KAAK;YAE5C,IAAI,KAAK,KAAK,KAAK,YACjB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG;QAE1C;IACF;IAEA,UAAoB;QAClB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,2FAA2F;QAC3F,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,IAAI,MAAM,qCAAe,CAAC,IAAI;QAC9B,IAAI,OAAO,qCAAe,CAAC,MAAM,EAAE;QACnC,IAAI,QAAQ,MACV,8CAA8C;QAC9C,OAAO,OAAO,GAAG,CAAC,EAAE,GAAG;QAGzB,IAAI,QAAQ,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAE5B,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC,EAAE,IAAK,KAAK,KAAK,KAAK,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE,EACvE;QAGF,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,sBAAsB,IAAqB,EAAU;QACnD,IAAI,QAAQ,kCAAY;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBAAqB,IAAqB,EAAU;QAClD,IAAI,QAAQ,kCAAY;QACxB,OAAO,SAAS,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACvD;;QAjGK,qBACL,aAAiC;;AAiGnC;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,KAAK,IAAI,KAAK,GAAG;QACnB,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,OAAO,qCAAe,CAAC,IAAI;IAC7B;AACF", "sources": ["packages/@internationalized/date/src/calendars/JapaneseCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst ERA_START_DATES = [[1868, 9, 8], [1912, 7, 30], [1926, 12, 25], [1989, 1, 8], [2019, 5, 1]];\nconst ERA_END_DATES = [[1912, 7, 29], [1926, 12, 24], [1989, 1, 7], [2019, 4, 30]];\nconst ERA_ADDENDS = [1867, 1911, 1925, 1988, 2018];\nconst ERA_NAMES = ['meiji', 'taisho', 'showa', 'heisei', 'reiwa'];\n\nfunction findEraFromGregorianDate(date: AnyCalendarDate) {\n  const idx = ERA_START_DATES.findIndex(([year, month, day]) => {\n    if (date.year < year) {\n      return true;\n    }\n\n    if (date.year === year && date.month < month) {\n      return true;\n    }\n\n    if (date.year === year && date.month === month && date.day < day) {\n      return true;\n    }\n\n    return false;\n  });\n\n  if (idx === -1) {\n    return ERA_START_DATES.length - 1;\n  }\n\n  if (idx === 0) {\n    return 0;\n  }\n\n  return idx - 1;\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let eraAddend = ERA_ADDENDS[ERA_NAMES.indexOf(date.era)];\n  if (!eraAddend) {\n    throw new Error('Unknown era: ' + date.era);\n  }\n\n  return new CalendarDate(\n    date.year + eraAddend,\n    date.month,\n    date.day\n  );\n}\n\n/**\n * The Japanese calendar is based on the Gregorian calendar, but with eras for the reign of each Japanese emperor.\n * Whenever a new emperor ascends to the throne, a new era begins and the year starts again from 1.\n * Note that eras before 1868 (Gregorian) are not currently supported by this implementation.\n */\nexport class JapaneseCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'japanese';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let era = findEraFromGregorianDate(date);\n\n    return new CalendarDate(\n      this,\n      ERA_NAMES[era],\n      date.year - ERA_ADDENDS[era],\n      date.month,\n      date.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let gregorianDate = toGregorian(date);\n    let era = findEraFromGregorianDate(gregorianDate);\n\n    if (ERA_NAMES[era] !== date.era) {\n      date.era = ERA_NAMES[era];\n      date.year = gregorianDate.year - ERA_ADDENDS[era];\n    }\n\n    // Constrain in case we went before the first supported era.\n    this.constrainDate(date);\n  }\n\n  constrainDate(date: Mutable<AnyCalendarDate>): void {\n    let idx = ERA_NAMES.indexOf(date.era);\n    let end = ERA_END_DATES[idx];\n    if (end != null) {\n      let [endYear, endMonth, endDay] = end;\n\n      // Constrain the year to the maximum possible value in the era.\n      // Then constrain the month and day fields within that.\n      let maxYear = endYear - ERA_ADDENDS[idx];\n      date.year = Math.max(1, Math.min(maxYear, date.year));\n      if (date.year === maxYear) {\n        date.month = Math.min(endMonth, date.month);\n\n        if (date.month === endMonth) {\n          date.day = Math.min(endDay, date.day);\n        }\n      }\n    }\n\n    if (date.year === 1 && idx >= 0) {\n      let [, startMonth, startDay] = ERA_START_DATES[idx];\n      date.month = Math.max(startMonth, date.month);\n\n      if (date.month === startMonth) {\n        date.day = Math.max(startDay, date.day);\n      }\n    }\n  }\n\n  getEras(): string[] {\n    return ERA_NAMES;\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // Get the number of years in the era, taking into account the date's month and day fields.\n    let era = ERA_NAMES.indexOf(date.era);\n    let cur = ERA_START_DATES[era];\n    let next = ERA_START_DATES[era + 1];\n    if (next == null) {\n      // 9999 gregorian is the maximum year allowed.\n      return 9999 - cur[0] + 1;\n    }\n\n    let years = next[0] - cur[0];\n\n    if (date.month < next[1] || (date.month === next[1] && date.day < next[2])) {\n      years++;\n    }\n\n    return years;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getMinimumMonthInYear(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start ? start[1] : 1;\n  }\n\n  getMinimumDayInMonth(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start && date.month === start[1] ? start[2] : 1;\n  }\n}\n\nfunction getMinimums(date: AnyCalendarDate) {\n  if (date.year === 1) {\n    let idx = ERA_NAMES.indexOf(date.era);\n    return ERA_START_DATES[idx];\n  }\n}\n"], "names": [], "version": 3, "file": "JapaneseCalendar.module.js.map"}