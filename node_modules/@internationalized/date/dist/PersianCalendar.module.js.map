{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAMhG,MAAM,sCAAgB;AAEtB,wEAAwE;AACxE,MAAM,oCAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAK,SAAS;CACf;AAQM,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,iBAAiB,KAAK;QAC1B,IAAI,OAAO,IAAI,KAAK,KAAK,CAAC,AAAC,CAAA,KAAK,iBAAiB,CAAA,IAAK;QACtD,IAAI,aAAa,MAAO,CAAA,OAAO,CAAA,IAAK,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,OAAO,EAAC,IAAK;QACjE,IAAI,YAAY,iBAAiB;QACjC,IAAI,QAAQ,YAAY,MACpB,KAAK,KAAK,CAAC,YAAY,MACvB,KAAK,KAAK,CAAC,AAAC,CAAA,YAAY,CAAA,IAAK;QACjC,IAAI,MAAM,YAAY,iCAAW,CAAC,MAAM,GAAG;QAC3C,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,MAAM,QAAQ,GAAG;IACjD;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,sCAAgB,IAAI,MAAO,CAAA,KAAK,IAAI,GAAG,CAAA,IAAK,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,KAAK,IAAI,GAAG,EAAC,IAAK;QACvF,MAAM,iCAAW,CAAC,KAAK,KAAK,GAAG,EAAE;QACjC,MAAM,KAAK,GAAG;QACd,OAAO;IACT;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,KAAK,IAAI,GAChB,OAAO;QAGT,IAAI,KAAK,KAAK,IAAI,IAChB,OAAO;QAGT,IAAI,aAAa,CAAA,GAAA,yCAAE,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,MAAM;QAChD,OAAO,aAAa,KAAK;IAC3B;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,gBAAwB;QACtB,8CAA8C;QAC9C,mDAAmD;QACnD,OAAO;IACT;;aA9CA,aAAiC;;AA+CnC", "sources": ["packages/@internationalized/date/src/calendars/PersianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod} from '../utils';\n\nconst PERSIAN_EPOCH = 1948320;\n\n// Number of days from the start of the year to the start of each month.\nconst MONTH_START = [\n  0, // Farvardin\n  31, // Ordibehesht\n  62, // Khordad\n  93, // Tir\n  124, // <PERSON><PERSON><PERSON>\n  155, // <PERSON><PERSON><PERSON>\n  186, // Mehr\n  216, // <PERSON><PERSON>\n  246, // Azar\n  276, // Dey\n  306, // Bahman\n  336  // Esfand\n];\n\n/**\n * The Persian calendar is the main calendar used in Iran and Afghanistan. It has 12 months\n * in each year, the first 6 of which have 31 days, and the next 5 have 30 days. The 12th month\n * has either 29 or 30 days depending on whether it is a leap year. The Persian year starts\n * around the March equinox.\n */\nexport class PersianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'persian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let daysSinceEpoch = jd - PERSIAN_EPOCH;\n    let year = 1 + Math.floor((33 * daysSinceEpoch + 3) / 12053);\n    let farvardin1 = 365 * (year - 1) + Math.floor((8 * year + 21) / 33);\n    let dayOfYear = daysSinceEpoch - farvardin1;\n    let month = dayOfYear < 216\n      ? Math.floor(dayOfYear / 31)\n      : Math.floor((dayOfYear - 6) / 30);\n    let day = dayOfYear - MONTH_START[month] + 1;\n    return new CalendarDate(this, year, month + 1, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = PERSIAN_EPOCH - 1 + 365 * (date.year - 1) + Math.floor((8 * date.year + 21) / 33);\n    jd += MONTH_START[date.month - 1];\n    jd += date.day;\n    return jd;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month <= 6) {\n      return 31;\n    }\n\n    if (date.month <= 11) {\n      return 30;\n    }\n\n    let isLeapYear = mod(25 * date.year + 11, 33) < 8;\n    return isLeapYear ? 30 : 29;\n  }\n\n  getEras(): string[] {\n    return ['AP'];\n  }\n\n  getYearsInEra(): number {\n    // 9378-10-10 persian is 9999-12-31 gregorian.\n    // Round down to 9377 to set the maximum full year.\n    return 9377;\n  }\n}\n"], "names": [], "version": 3, "file": "PersianCalendar.module.js.map"}