{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAOhG,MAAM,yCAAmB;AAEzB,SAAS,oCAAc,IAAqB;IAC1C,OAAO,KAAK,GAAG,KAAK,WAChB,KAAK,IAAI,GAAG,yCACZ,IAAI,KAAK,IAAI,GAAG;AACtB;AAEA,SAAS,wCAAkB,IAAY;IACrC,IAAI,IAAI,OAAO;IACf,IAAI,IAAI,GACN,OAAO;QAAC;QAAU;KAAE;SAEpB,OAAO;QAAC;QAAiB,IAAI;KAAE;AAEnC;AAOO,MAAM,kDAAuB,CAAA,GAAA,yCAAgB;IAGlD,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK,CAAC,cAAc;QAC/B,IAAI,eAAe,CAAA,GAAA,yCAAc,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;QACtD,IAAI,CAAC,KAAK,KAAK,GAAG,wCAAkB;QACpC,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,KAAK,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/D;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAiB;SAAS;IACpC;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,CAAC,KAAK,KAAK,GAAG,wCAAkB,oCAAc;QAClD,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;IACd;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,KAAK,GAAG,KAAK,kBAAkB,OAAO,OAAO;IACtD;;QAlCK,qBACL,aAAiC,MAAO,oBAAoB;;;AAkC9D;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,GAAA,yCAAe,EAAE,oCAAc;IACjD,OAAO,IAAI,CAAA,GAAA,yCAAW,EACpB,KACA,MACA,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ", "sources": ["packages/@internationalized/date/src/calendars/TaiwanCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst TAIWAN_ERA_START = 1911;\n\nfunction gregorianYear(date: AnyCalendarDate) {\n  return date.era === 'minguo'\n    ? date.year + TAIWAN_ERA_START\n    : 1 - date.year + TAIWAN_ERA_START;\n}\n\nfunction gregorianToTaiwan(year: number): [string, number] {\n  let y = year - TAIWAN_ERA_START;\n  if (y > 0) {\n    return ['minguo', y];\n  } else {\n    return ['before_minguo', 1 - y];\n  }\n}\n\n/**\n * The Taiwanese calendar is the same as the Gregorian calendar, but years\n * are numbered starting from 1912 (Gregorian). Two eras are supported:\n * 'before_minguo' and 'minguo'.\n */\nexport class TaiwanCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'roc'; // Republic of China\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let extendedYear = getExtendedYear(date.era, date.year);\n    let [era, year] = gregorianToTaiwan(extendedYear);\n    return new CalendarDate(this, era, year, date.month, date.day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['before_minguo', 'minguo'];\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let [era, year] = gregorianToTaiwan(gregorianYear(date));\n    date.era = era;\n    date.year = year;\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'before_minguo';\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    return date.era === 'before_minguo' ? 9999 : 9999 - TAIWAN_ERA_START;\n  }\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(gregorianYear(date));\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n"], "names": [], "version": 3, "file": "TaiwanCalendar.module.js.map"}