{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAED,uFAAuF;AACvF,gGAAgG;;;;;AASzF,SAAS,yCAAc,IAAiB;IAC7C,OAAO,0CAAW,MAAM,IAAI,CAAA,GAAA,yCAAgB;IAC5C,IAAI,OAAO,CAAA,GAAA,yCAAc,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;IAC9C,OAAO,qCAAe,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,WAAW;AACzG;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa,EAAE,GAAW,EAAE,IAAY,EAAE,MAAc,EAAE,MAAc,EAAE,WAAmB;IACjI,sEAAsE;IACtE,gCAAgC;IAChC,IAAI,OAAO,IAAI;IACf,KAAK,WAAW,CAAC,MAAM,QAAQ,QAAQ;IACvC,KAAK,cAAc,CAAC,MAAM,QAAQ,GAAG;IACrC,OAAO,KAAK,OAAO;AACrB;AAEO,SAAS,0CAAkB,EAAU,EAAE,QAAgB;IAC5D,qBAAqB;IACrB,IAAI,aAAa,OACf,OAAO;IAGT,6DAA6D;IAC7D,IAAI,KAAK,KAAK,aAAa,CAAA,GAAA,yCAAe,KACxC,OAAO,IAAI,KAAK,IAAI,iBAAiB,KAA9B;IAGT,IAAI,QAAC,IAAI,SAAE,KAAK,OAAE,GAAG,QAAE,IAAI,UAAE,MAAM,UAAE,MAAM,EAAC,GAAG,uCAAiB,IAAI;IACpE,IAAI,MAAM,qCAAe,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ;IACjE,OAAO,MAAM,KAAK,KAAK,CAAC,KAAK,QAAQ;AACvC;AAEA,MAAM,6CAAuB,IAAI;AAEjC,SAAS,uCAAiB,EAAU,EAAE,QAAgB;IACpD,IAAI,YAAY,2CAAqB,GAAG,CAAC;IACzC,IAAI,CAAC,WAAW;QACd,YAAY,IAAI,KAAK,cAAc,CAAC,SAAS;sBAC3C;YACA,QAAQ;YACR,KAAK;YACL,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QAEA,2CAAqB,GAAG,CAAC,UAAU;IACrC;IAEA,IAAI,QAAQ,UAAU,aAAa,CAAC,IAAI,KAAK;IAC7C,IAAI,aAAuC,CAAC;IAC5C,KAAK,IAAI,QAAQ,MACf,IAAI,KAAK,IAAI,KAAK,WAChB,UAAU,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK;IAKtC,OAAO;QACL,0FAA0F;QAC1F,MAAM,WAAW,GAAG,KAAK,QAAQ,WAAW,GAAG,KAAK,MAAM,CAAC,WAAW,IAAI,GAAG,IAAI,CAAC,WAAW,IAAI;QACjG,OAAO,CAAC,WAAW,KAAK;QACxB,KAAK,CAAC,WAAW,GAAG;QACpB,MAAM,WAAW,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,IAAI;QACrD,QAAQ,CAAC,WAAW,MAAM;QAC1B,QAAQ,CAAC,WAAW,MAAM;IAC5B;AACF;AAEA,MAAM,kCAAY;AAEX,SAAS,0CAAkB,IAAsB,EAAE,QAAgB;IACxE,IAAI,KAAK,yCAAc;IACvB,IAAI,UAAU,KAAK,0CAAkB,KAAK,iCAAW;IACrD,IAAI,QAAQ,KAAK,0CAAkB,KAAK,iCAAW;IACnD,OAAO,wCAAkB,MAAM,UAAU,SAAS;AACpD;AAEA,SAAS,wCAAkB,IAAsB,EAAE,QAAgB,EAAE,OAAe,EAAE,KAAa;IACjG,IAAI,QAAQ,YAAY,QAAQ;QAAC;KAAQ,GAAG;QAAC;QAAS;KAAM;IAC5D,OAAO,MAAM,MAAM,CAAC,CAAA,WAAY,sCAAgB,MAAM,UAAU;AAClE;AAEA,SAAS,sCAAgB,IAAsB,EAAE,QAAgB,EAAE,QAAgB;IACjF,IAAI,QAAQ,uCAAiB,UAAU;IACvC,OAAO,KAAK,IAAI,KAAK,MAAM,IAAI,IAC1B,KAAK,KAAK,KAAK,MAAM,KAAK,IAC1B,KAAK,GAAG,KAAK,MAAM,GAAG,IACtB,KAAK,IAAI,KAAK,MAAM,IAAI,IACxB,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,MAAM,KAAK,MAAM,MAAM;AACnC;AAEO,SAAS,0CAAW,IAAqC,EAAE,QAAgB,EAAE,iBAAiC,YAAY;IAC/H,IAAI,WAAW,0CAAmB;IAElC,uDAAuD;IACvD,IAAI,aAAa,OACf,OAAO,yCAAc;IAGvB,uGAAuG;IACvG,IAAI,aAAa,CAAA,GAAA,yCAAe,OAAO,mBAAmB,cAAc;QACtE,WAAW,0CAAW,UAAU,IAAI,CAAA,GAAA,yCAAgB;QAEpD,+FAA+F;QAC/F,IAAI,OAAO,IAAI;QACf,IAAI,OAAO,CAAA,GAAA,yCAAc,EAAE,SAAS,GAAG,EAAE,SAAS,IAAI;QACtD,KAAK,WAAW,CAAC,MAAM,SAAS,KAAK,GAAG,GAAG,SAAS,GAAG;QACvD,KAAK,QAAQ,CAAC,SAAS,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,MAAM,EAAE,SAAS,WAAW;QACnF,OAAO,KAAK,OAAO;IACrB;IAEA,IAAI,KAAK,yCAAc;IACvB,IAAI,eAAe,0CAAkB,KAAK,iCAAW;IACrD,IAAI,cAAc,0CAAkB,KAAK,iCAAW;IACpD,IAAI,QAAQ,wCAAkB,UAAU,UAAU,KAAK,cAAc,KAAK;IAE1E,IAAI,MAAM,MAAM,KAAK,GACnB,OAAO,KAAK,CAAC,EAAE;IAGjB,IAAI,MAAM,MAAM,GAAG,GACjB,OAAQ;QACN,2DAA2D;QAC3D,KAAK;QACL,KAAK;YACH,OAAO,KAAK,CAAC,EAAE;QACjB,KAAK;YACH,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAChC,KAAK;YACH,MAAM,IAAI,WAAW;IACzB;IAGF,OAAQ;QACN,KAAK;YACH,OAAO,KAAK,GAAG,CAAC,KAAK,cAAc,KAAK;QAC1C,8DAA8D;QAC9D,KAAK;QACL,KAAK;YACH,OAAO,KAAK,GAAG,CAAC,KAAK,cAAc,KAAK;QAC1C,KAAK;YACH,MAAM,IAAI,WAAW;IACzB;AACF;AAEO,SAAS,0CAAO,QAAyC,EAAE,QAAgB,EAAE,iBAAiC,YAAY;IAC/H,OAAO,IAAI,KAAK,0CAAW,UAAU,UAAU;AACjD;AAKO,SAAS,0CAAa,EAAU,EAAE,QAAgB;IACvD,IAAI,SAAS,0CAAkB,IAAI;IACnC,IAAI,OAAO,IAAI,KAAK,KAAK;IACzB,IAAI,OAAO,KAAK,cAAc;IAC9B,IAAI,QAAQ,KAAK,WAAW,KAAK;IACjC,IAAI,MAAM,KAAK,UAAU;IACzB,IAAI,OAAO,KAAK,WAAW;IAC3B,IAAI,SAAS,KAAK,aAAa;IAC/B,IAAI,SAAS,KAAK,aAAa;IAC/B,IAAI,cAAc,KAAK,kBAAkB;IAEzC,OAAO,IAAI,CAAA,GAAA,yCAAY,EAAE,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,KAAK,UAAU,QAAQ,MAAM,QAAQ,QAAQ;AACpI;AAKO,SAAS,0CAAS,IAAU,EAAE,QAAgB;IACnD,OAAO,0CAAa,KAAK,OAAO,IAAI;AACtC;AAEO,SAAS,0CAAgB,IAAU;IACxC,OAAO,0CAAS,MAAM,CAAA,GAAA,yCAAe;AACvC;AAGO,SAAS,0CAAe,QAAyB;IACtD,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,SAAS,QAAQ,EAAE,SAAS,GAAG,EAAE,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,GAAG;AACtG;AAEO,SAAS,0CAAa,IAAqB;IAChD,OAAO;QACL,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,IAAI;QACf,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;IACf;AACF;AAEO,SAAS,0CAAa,IAAa;IACxC,OAAO;QACL,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;QACnB,aAAa,KAAK,WAAW;IAC/B;AACF;AAMO,SAAS,0CAAmB,IAAqD,EAAE,IAAc;IACtG,IAAI,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc;IACpD,IAAI,cAAc,MACf,CAAA,QAAC,IAAI,UAAE,MAAM,UAAE,MAAM,eAAE,WAAW,EAAC,GAAG,IAAG;SACrC,IAAI,UAAU,QAAQ,CAAC,MAC5B,OAAO;IAGT,IAAI,MACD,CAAA,QAAC,IAAI,UAAE,MAAM,UAAE,MAAM,eAAE,WAAW,EAAC,GAAG,IAAG;IAG5C,OAAO,IAAI,CAAA,GAAA,yCAAe,EACxB,KAAK,QAAQ,EACb,KAAK,GAAG,EACR,KAAK,IAAI,EACT,KAAK,KAAK,EACV,KAAK,GAAG,EACR,MACA,QACA,QACA;AAEJ;AAGO,SAAS,0CAAO,QAA0C;IAC/D,OAAO,IAAI,CAAA,GAAA,wCAAG,EAAE,SAAS,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,MAAM,EAAE,SAAS,WAAW;AACvF;AAGO,SAAS,0CAAsC,IAAO,EAAE,QAAkB;IAC/E,IAAI,CAAA,GAAA,wCAAc,EAAE,KAAK,QAAQ,EAAE,WACjC,OAAO;IAGT,IAAI,eAAe,SAAS,aAAa,CAAC,KAAK,QAAQ,CAAC,WAAW,CAAC;IACpE,IAAI,OAAmB,KAAK,IAAI;IAChC,KAAK,QAAQ,GAAG;IAChB,KAAK,GAAG,GAAG,aAAa,GAAG;IAC3B,KAAK,IAAI,GAAG,aAAa,IAAI;IAC7B,KAAK,KAAK,GAAG,aAAa,KAAK;IAC/B,KAAK,GAAG,GAAG,aAAa,GAAG;IAC3B,CAAA,GAAA,yCAAQ,EAAE;IACV,OAAO;AACT;AAMO,SAAS,0CAAQ,IAAqD,EAAE,QAAgB,EAAE,cAA+B;IAC9H,IAAI,gBAAgB,CAAA,GAAA,yCAAY,GAAG;QACjC,IAAI,KAAK,QAAQ,KAAK,UACpB,OAAO;QAGT,OAAO,0CAAW,MAAM;IAC1B;IAEA,IAAI,KAAK,0CAAW,MAAM,UAAU;IACpC,OAAO,0CAAa,IAAI;AAC1B;AAEO,SAAS,yCAAY,IAAmB;IAC7C,IAAI,KAAK,yCAAc,QAAQ,KAAK,MAAM;IAC1C,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS,0CAAW,IAAmB,EAAE,QAAgB;IAC9D,IAAI,KAAK,yCAAc,QAAQ,KAAK,MAAM;IAC1C,OAAO,0CAAW,0CAAa,IAAI,WAAW,KAAK,QAAQ;AAC7D;AAGO,SAAS,0CAAgB,IAAmB;IACjD,OAAO,0CAAW,MAAM,CAAA,GAAA,yCAAe;AACzC", "sources": ["packages/@internationalized/date/src/conversion.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, Calendar, DateFields, Disambiguation, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {constrain} from './manipulation';\nimport {getExtendedYear, GregorianCalendar} from './calendars/GregorianCalendar';\nimport {getLocalTimeZone, isEqualCalendar} from './queries';\nimport {Mutable} from './utils';\n\nexport function epochFromDate(date: AnyDateTime): number {\n  date = toCalendar(date, new GregorianCalendar());\n  let year = getExtendedYear(date.era, date.year);\n  return epochFromParts(year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);\n}\n\nfunction epochFromParts(year: number, month: number, day: number, hour: number, minute: number, second: number, millisecond: number): number {\n  // Note: Date.UTC() interprets one and two-digit years as being in the\n  // 20th century, so don't use it\n  let date = new Date();\n  date.setUTCHours(hour, minute, second, millisecond);\n  date.setUTCFullYear(year, month - 1, day);\n  return date.getTime();\n}\n\nexport function getTimeZoneOffset(ms: number, timeZone: string): number {\n  // Fast path for UTC.\n  if (timeZone === 'UTC') {\n    return 0;\n  }\n\n  // Fast path: for local timezone after 1970, use native Date.\n  if (ms > 0 && timeZone === getLocalTimeZone()) {\n    return new Date(ms).getTimezoneOffset() * -60 * 1000;\n  }\n\n  let {year, month, day, hour, minute, second} = getTimeZoneParts(ms, timeZone);\n  let utc = epochFromParts(year, month, day, hour, minute, second, 0);\n  return utc - Math.floor(ms / 1000) * 1000;\n}\n\nconst formattersByTimeZone = new Map<string, Intl.DateTimeFormat>();\n\nfunction getTimeZoneParts(ms: number, timeZone: string) {\n  let formatter = formattersByTimeZone.get(timeZone);\n  if (!formatter) {\n    formatter = new Intl.DateTimeFormat('en-US', {\n      timeZone,\n      hour12: false,\n      era: 'short',\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric',\n      hour: 'numeric',\n      minute: 'numeric',\n      second: 'numeric'\n    });\n\n    formattersByTimeZone.set(timeZone, formatter);\n  }\n\n  let parts = formatter.formatToParts(new Date(ms));\n  let namedParts: {[name: string]: string} = {};\n  for (let part of parts) {\n    if (part.type !== 'literal') {\n      namedParts[part.type] = part.value;\n    }\n  }\n\n\n  return {\n    // Firefox returns B instead of BC... https://bugzilla.mozilla.org/show_bug.cgi?id=1752253\n    year: namedParts.era === 'BC' || namedParts.era === 'B' ? -namedParts.year + 1 : +namedParts.year,\n    month: +namedParts.month,\n    day: +namedParts.day,\n    hour: namedParts.hour === '24' ? 0 : +namedParts.hour, // bugs.chromium.org/p/chromium/issues/detail?id=1045791\n    minute: +namedParts.minute,\n    second: +namedParts.second\n  };\n}\n\nconst DAYMILLIS = 86400000;\n\nexport function possibleAbsolutes(date: CalendarDateTime, timeZone: string): number[] {\n  let ms = epochFromDate(date);\n  let earlier = ms - getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let later = ms - getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  return getValidWallTimes(date, timeZone, earlier, later);\n}\n\nfunction getValidWallTimes(date: CalendarDateTime, timeZone: string, earlier: number, later: number): number[] {\n  let found = earlier === later ? [earlier] : [earlier, later];\n  return found.filter(absolute => isValidWallTime(date, timeZone, absolute));\n}\n\nfunction isValidWallTime(date: CalendarDateTime, timeZone: string, absolute: number) {\n  let parts = getTimeZoneParts(absolute, timeZone);\n  return date.year === parts.year\n    && date.month === parts.month\n    && date.day === parts.day\n    && date.hour === parts.hour\n    && date.minute === parts.minute\n    && date.second === parts.second;\n}\n\nexport function toAbsolute(date: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): number {\n  let dateTime = toCalendarDateTime(date);\n\n  // Fast path: if the time zone is UTC, use native Date.\n  if (timeZone === 'UTC') {\n    return epochFromDate(dateTime);\n  }\n\n  // Fast path: if the time zone is the local timezone and disambiguation is compatible, use native Date.\n  if (timeZone === getLocalTimeZone() && disambiguation === 'compatible') {\n    dateTime = toCalendar(dateTime, new GregorianCalendar());\n\n    // Don't use Date constructor here because two-digit years are interpreted in the 20th century.\n    let date = new Date();\n    let year = getExtendedYear(dateTime.era, dateTime.year);\n    date.setFullYear(year, dateTime.month - 1, dateTime.day);\n    date.setHours(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n    return date.getTime();\n  }\n\n  let ms = epochFromDate(dateTime);\n  let offsetBefore = getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let offsetAfter = getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  let valid = getValidWallTimes(dateTime, timeZone, ms - offsetBefore, ms - offsetAfter);\n\n  if (valid.length === 1) {\n    return valid[0];\n  }\n\n  if (valid.length > 1) {\n    switch (disambiguation) {\n      // 'compatible' means 'earlier' for \"fall back\" transitions\n      case 'compatible':\n      case 'earlier':\n        return valid[0];\n      case 'later':\n        return valid[valid.length - 1];\n      case 'reject':\n        throw new RangeError('Multiple possible absolute times found');\n    }\n  }\n\n  switch (disambiguation) {\n    case 'earlier':\n      return Math.min(ms - offsetBefore, ms - offsetAfter);\n    // 'compatible' means 'later' for \"spring forward\" transitions\n    case 'compatible':\n    case 'later':\n      return Math.max(ms - offsetBefore, ms - offsetAfter);\n    case 'reject':\n      throw new RangeError('No such absolute time found');\n  }\n}\n\nexport function toDate(dateTime: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): Date {\n  return new Date(toAbsolute(dateTime, timeZone, disambiguation));\n}\n\n/**\n * Takes a Unix epoch (milliseconds since 1970) and converts it to the provided time zone.\n */\nexport function fromAbsolute(ms: number, timeZone: string): ZonedDateTime {\n  let offset = getTimeZoneOffset(ms, timeZone);\n  let date = new Date(ms + offset);\n  let year = date.getUTCFullYear();\n  let month = date.getUTCMonth() + 1;\n  let day = date.getUTCDate();\n  let hour = date.getUTCHours();\n  let minute = date.getUTCMinutes();\n  let second = date.getUTCSeconds();\n  let millisecond = date.getUTCMilliseconds();\n\n  return new ZonedDateTime(year < 1 ? 'BC' : 'AD', year < 1 ? -year + 1 : year, month, day, timeZone, offset, hour, minute, second, millisecond);\n}\n\n/**\n * Takes a `Date` object and converts it to the provided time zone.\n */\nexport function fromDate(date: Date, timeZone: string): ZonedDateTime {\n  return fromAbsolute(date.getTime(), timeZone);\n}\n\nexport function fromDateToLocal(date: Date): ZonedDateTime {\n  return fromDate(date, getLocalTimeZone());\n}\n\n/** Converts a value with date components such as a `CalendarDateTime` or `ZonedDateTime` into a `CalendarDate`. */\nexport function toCalendarDate(dateTime: AnyCalendarDate): CalendarDate {\n  return new CalendarDate(dateTime.calendar, dateTime.era, dateTime.year, dateTime.month, dateTime.day);\n}\n\nexport function toDateFields(date: AnyCalendarDate): DateFields {\n  return {\n    era: date.era,\n    year: date.year,\n    month: date.month,\n    day: date.day\n  };\n}\n\nexport function toTimeFields(date: AnyTime): TimeFields {\n  return {\n    hour: date.hour,\n    minute: date.minute,\n    second: date.second,\n    millisecond: date.millisecond\n  };\n}\n\n/**\n * Converts a date value to a `CalendarDateTime`. An optional `Time` value can be passed to set the time\n * of the resulting value, otherwise it will default to midnight.\n */\nexport function toCalendarDateTime(date: CalendarDate | CalendarDateTime | ZonedDateTime, time?: AnyTime): CalendarDateTime {\n  let hour = 0, minute = 0, second = 0, millisecond = 0;\n  if ('timeZone' in date) {\n    ({hour, minute, second, millisecond} = date);\n  } else if ('hour' in date && !time) {\n    return date;\n  }\n\n  if (time) {\n    ({hour, minute, second, millisecond} = time);\n  }\n\n  return new CalendarDateTime(\n    date.calendar,\n    date.era,\n    date.year,\n    date.month,\n    date.day,\n    hour,\n    minute,\n    second,\n    millisecond\n  );\n}\n\n/** Extracts the time components from a value containing a date and time. */\nexport function toTime(dateTime: CalendarDateTime | ZonedDateTime): Time {\n  return new Time(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n}\n\n/** Converts a date from one calendar system to another. */\nexport function toCalendar<T extends AnyCalendarDate>(date: T, calendar: Calendar): T {\n  if (isEqualCalendar(date.calendar, calendar)) {\n    return date;\n  }\n\n  let calendarDate = calendar.fromJulianDay(date.calendar.toJulianDay(date));\n  let copy: Mutable<T> = date.copy();\n  copy.calendar = calendar;\n  copy.era = calendarDate.era;\n  copy.year = calendarDate.year;\n  copy.month = calendarDate.month;\n  copy.day = calendarDate.day;\n  constrain(copy);\n  return copy;\n}\n\n/**\n * Converts a date value to a `ZonedDateTime` in the provided time zone. The `disambiguation` option can be set\n * to control how values that fall on daylight saving time changes are interpreted.\n */\nexport function toZoned(date: CalendarDate | CalendarDateTime | ZonedDateTime, timeZone: string, disambiguation?: Disambiguation): ZonedDateTime {\n  if (date instanceof ZonedDateTime) {\n    if (date.timeZone === timeZone) {\n      return date;\n    }\n\n    return toTimeZone(date, timeZone);\n  }\n\n  let ms = toAbsolute(date, timeZone, disambiguation);\n  return fromAbsolute(ms, timeZone);\n}\n\nexport function zonedToDate(date: ZonedDateTime): Date {\n  let ms = epochFromDate(date) - date.offset;\n  return new Date(ms);\n}\n\n/** Converts a `ZonedDateTime` from one time zone to another. */\nexport function toTimeZone(date: ZonedDateTime, timeZone: string): ZonedDateTime {\n  let ms = epochFromDate(date) - date.offset;\n  return toCalendar(fromAbsolute(ms, timeZone), date.calendar);\n}\n\n/** Converts the given `ZonedDateTime` into the user's local time zone. */\nexport function toLocalTimeZone(date: ZonedDateTime): ZonedDateTime {\n  return toTimeZone(date, getLocalTimeZone());\n}\n"], "names": [], "version": 3, "file": "conversion.module.js.map"}