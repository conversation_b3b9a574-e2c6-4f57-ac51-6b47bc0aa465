var $561c4ef058278b74$exports = require("./BuddhistCalendar.main.js");
var $4db04d1051af0f2f$exports = require("./EthiopicCalendar.main.js");
var $af14c9812fdceb33$exports = require("./GregorianCalendar.main.js");
var $0f5324ee3bdd9396$exports = require("./HebrewCalendar.main.js");
var $5f1dfa5c67609fe6$exports = require("./IndianCalendar.main.js");
var $ecb2c4cc8c9aae25$exports = require("./IslamicCalendar.main.js");
var $b0ac0602ef646b2c$exports = require("./JapaneseCalendar.main.js");
var $3c060181fc7249ae$exports = require("./PersianCalendar.main.js");
var $9cc5d3577ec40243$exports = require("./TaiwanCalendar.main.js");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "createCalendar", () => $4922c0a5a69da0ba$export$dd0bbc9b26defe37);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 








function $4922c0a5a69da0ba$export$dd0bbc9b26defe37(name) {
    switch(name){
        case 'buddhist':
            return new (0, $561c4ef058278b74$exports.BuddhistCalendar)();
        case 'ethiopic':
            return new (0, $4db04d1051af0f2f$exports.EthiopicCalendar)();
        case 'ethioaa':
            return new (0, $4db04d1051af0f2f$exports.EthiopicAmeteAlemCalendar)();
        case 'coptic':
            return new (0, $4db04d1051af0f2f$exports.CopticCalendar)();
        case 'hebrew':
            return new (0, $0f5324ee3bdd9396$exports.HebrewCalendar)();
        case 'indian':
            return new (0, $5f1dfa5c67609fe6$exports.IndianCalendar)();
        case 'islamic-civil':
            return new (0, $ecb2c4cc8c9aae25$exports.IslamicCivilCalendar)();
        case 'islamic-tbla':
            return new (0, $ecb2c4cc8c9aae25$exports.IslamicTabularCalendar)();
        case 'islamic-umalqura':
            return new (0, $ecb2c4cc8c9aae25$exports.IslamicUmalquraCalendar)();
        case 'japanese':
            return new (0, $b0ac0602ef646b2c$exports.JapaneseCalendar)();
        case 'persian':
            return new (0, $3c060181fc7249ae$exports.PersianCalendar)();
        case 'roc':
            return new (0, $9cc5d3577ec40243$exports.TaiwanCalendar)();
        case 'gregory':
        default:
            return new (0, $af14c9812fdceb33$exports.GregorianCalendar)();
    }
}


//# sourceMappingURL=createCalendar.main.js.map
