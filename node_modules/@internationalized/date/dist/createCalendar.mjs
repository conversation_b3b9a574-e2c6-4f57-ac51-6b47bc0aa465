import {BuddhistCalendar as $8d73d47422ca7302$export$42d20a78301dee44} from "./BuddhistCalendar.mjs";
import {CopticCalendar as $b956b2d7a6cf451f$export$fe6243cbe1a4b7c1, EthiopicAmeteAlemCalendar as $b956b2d7a6cf451f$export$d72e0c37005a4914, EthiopicCalendar as $b956b2d7a6cf451f$export$26ba6eab5e20cd7d} from "./EthiopicCalendar.mjs";
import {GregorianCalendar as $3b62074eb05584b2$export$80ee6245ec4f29ec} from "./GregorianCalendar.mjs";
import {HebrewCalendar as $7c5f6fbf42389787$export$ca405048b8fb5af} from "./HebrewCalendar.mjs";
import {IndianCalendar as $82c358003bdda0a8$export$39f31c639fa15726} from "./IndianCalendar.mjs";
import {IslamicCivilCalendar as $f2f3e0e3a817edbd$export$2066795aadd37bfc, IslamicTabularCalendar as $f2f3e0e3a817edbd$export$37f0887f2f9d22f7, IslamicUmalquraCalendar as $f2f3e0e3a817edbd$export$5baab4758c231076} from "./IslamicCalendar.mjs";
import {JapaneseCalendar as $62225008020f0a13$export$b746ab2b60cdffbf} from "./JapaneseCalendar.mjs";
import {PersianCalendar as $f3ed2e4472ae7e25$export$37fccdbfd14c5939} from "./PersianCalendar.mjs";
import {TaiwanCalendar as $5f31bd6f0c8940b2$export$65e01080afcb0799} from "./TaiwanCalendar.mjs";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 








function $64244302c3013299$export$dd0bbc9b26defe37(name) {
    switch(name){
        case 'buddhist':
            return new (0, $8d73d47422ca7302$export$42d20a78301dee44)();
        case 'ethiopic':
            return new (0, $b956b2d7a6cf451f$export$26ba6eab5e20cd7d)();
        case 'ethioaa':
            return new (0, $b956b2d7a6cf451f$export$d72e0c37005a4914)();
        case 'coptic':
            return new (0, $b956b2d7a6cf451f$export$fe6243cbe1a4b7c1)();
        case 'hebrew':
            return new (0, $7c5f6fbf42389787$export$ca405048b8fb5af)();
        case 'indian':
            return new (0, $82c358003bdda0a8$export$39f31c639fa15726)();
        case 'islamic-civil':
            return new (0, $f2f3e0e3a817edbd$export$2066795aadd37bfc)();
        case 'islamic-tbla':
            return new (0, $f2f3e0e3a817edbd$export$37f0887f2f9d22f7)();
        case 'islamic-umalqura':
            return new (0, $f2f3e0e3a817edbd$export$5baab4758c231076)();
        case 'japanese':
            return new (0, $62225008020f0a13$export$b746ab2b60cdffbf)();
        case 'persian':
            return new (0, $f3ed2e4472ae7e25$export$37fccdbfd14c5939)();
        case 'roc':
            return new (0, $5f31bd6f0c8940b2$export$65e01080afcb0799)();
        case 'gregory':
        default:
            return new (0, $3b62074eb05584b2$export$80ee6245ec4f29ec)();
    }
}


export {$64244302c3013299$export$dd0bbc9b26defe37 as createCalendar};
//# sourceMappingURL=createCalendar.module.js.map
