{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAUM,SAAS,0CAAU,CAAY,EAAE,CAAY;IAClD,IAAI,CAAA,GAAA,yCAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;AACvF;AAGO,SAAS,0CAAY,CAAY,EAAE,CAAY;IACpD,IAAI,CAAA,GAAA,yCAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,yGAAyG;IACzG,IAAI,0CAAa;IACjB,IAAI,0CAAa;IACjB,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK;AACpE;AAGO,SAAS,0CAAW,CAAY,EAAE,CAAY;IACnD,IAAI,CAAA,GAAA,yCAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,IAAI,0CAAY;IAChB,IAAI,0CAAY;IAChB,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAC7C;AAGO,SAAS,0CAAW,CAAY,EAAE,CAAY;IACnD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAU,GAAG;AACjE;AAGO,SAAS,0CAAa,CAAY,EAAE,CAAY;IACrD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAY,GAAG;AACnE;AAGO,SAAS,0CAAY,CAAY,EAAE,CAAY;IACpD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAW,GAAG;AAClE;AAGO,SAAS,yCAAgB,CAAW,EAAE,CAAW;QAC/C,YAAkB;QAAlB,aAAA;IAAP,OAAO,CAAA,OAAA,CAAA,eAAA,aAAA,EAAE,OAAO,cAAT,iCAAA,gBAAA,GAAY,gBAAZ,yBAAA,eAAkB,aAAA,EAAE,OAAO,cAAT,iCAAA,gBAAA,GAAY,gBAA9B,kBAAA,OAAoC,EAAE,UAAU,KAAK,EAAE,UAAU;AAC1E;AAGO,SAAS,0CAAQ,IAAe,EAAE,QAAgB;IACvD,OAAO,0CAAU,MAAM,0CAAM;AAC/B;AAEA,MAAM,gCAAU;IACd,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AASO,SAAS,0CAAa,IAAe,EAAE,MAAc,EAAE,cAA0B;IACtF,IAAI,SAAS,KAAK,QAAQ,CAAC,WAAW,CAAC;IAEvC,wEAAwE;IACxE,wCAAwC;IACxC,IAAI,YAAY,iBAAiB,6BAAO,CAAC,eAAe,GAAG,mCAAa;IACxE,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,IAAI,aAAa;IACpD,IAAI,YAAY,GACd,aAAa;IAGf,OAAO;AACT;AAGO,SAAS,yCAAI,QAAgB;IAClC,OAAO,CAAA,GAAA,yCAAW,EAAE,KAAK,GAAG,IAAI;AAClC;AAGO,SAAS,0CAAM,QAAgB;IACpC,OAAO,CAAA,GAAA,yCAAa,EAAE,yCAAI;AAC5B;AAEO,SAAS,0CAAY,CAAkB,EAAE,CAAkB;IAChE,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;AAC5D;AAEO,SAAS,0CAAY,CAAU,EAAE,CAAU;IAChD,OAAO,+BAAS,KAAK,+BAAS;AAChC;AAEA,SAAS,+BAAS,CAAU;IAC1B,OAAO,EAAE,IAAI,GAAN,UAA0B,EAAE,MAAM,GAAR,QAAuB,EAAE,MAAM,GAAG,OAAO,EAAE,WAAW;AACzF;AAMO,SAAS,wCAAc,CAAe,EAAE,QAAgB;IAC7D,IAAI,KAAK,CAAA,GAAA,yCAAS,EAAE,GAAG;IACvB,IAAI,WAAW,EAAE,GAAG,CAAC;QAAC,MAAM;IAAC;IAC7B,IAAI,aAAa,CAAA,GAAA,yCAAS,EAAE,UAAU;IACtC,OAAO,AAAC,CAAA,aAAa,EAAC,IAAK;AAC7B;AAEA,IAAI,sCAA+B;AAG5B,SAAS;IACd,iCAAiC;IACjC,IAAI,uCAAiB,MACnB,sCAAgB,IAAI,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAGtE,OAAO;AACT;AAOO,SAAS,0CAAa,IAAe;IAC1C,yEAAyE;IACzE,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM,KAAK,GAAG,GAAG;IAAC;AAC1C;AAOO,SAAS,0CAAW,IAAe;IACxC,OAAO,KAAK,GAAG,CAAC;QAAC,MAAM,KAAK,QAAQ,CAAC,cAAc,CAAC,QAAQ,KAAK,GAAG;IAAA;AACtE;AAOO,SAAS,0CAAY,IAAe;IACzC,OAAO,0CAAa,KAAK,QAAQ,CAAC;QAAC,QAAQ,KAAK,KAAK,GAAG;IAAC;AAC3D;AAOO,SAAS,0CAAU,IAAe;IACvC,OAAO,0CAAW,KAAK,GAAG,CAAC;QAAC,QAAQ,KAAK,QAAQ,CAAC,eAAe,CAAC,QAAQ,KAAK,KAAK;IAAA;AACtF;AAEO,SAAS,0CAAsB,IAAqB;IACzD,IAAI,KAAK,QAAQ,CAAC,qBAAqB,EACrC,OAAO,KAAK,QAAQ,CAAC,qBAAqB,CAAC;IAG7C,OAAO;AACT;AAEO,SAAS,0CAAqB,IAAqB;IACxD,IAAI,KAAK,QAAQ,CAAC,oBAAoB,EACpC,OAAO,KAAK,QAAQ,CAAC,oBAAoB,CAAC;IAG5C,OAAO;AACT;AAOO,SAAS,0CAAY,IAAe,EAAE,MAAc,EAAE,cAA0B;IACrF,IAAI,YAAY,0CAAa,MAAM,QAAQ;IAC3C,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM;IAAS;AACvC;AAOO,SAAS,0CAAU,IAAe,EAAE,MAAc,EAAE,cAA0B;IACnF,OAAO,0CAAY,MAAM,QAAQ,gBAAgB,GAAG,CAAC;QAAC,MAAM;IAAC;AAC/D;AAEA,MAAM,sCAAgB,IAAI;AAE1B,SAAS,gCAAU,MAAc;IAC/B,gFAAgF;IAChF,aAAa;IACb,IAAI,KAAK,MAAM,EAAE;QACf,iEAAiE;QACjE,IAAI,SAAS,oCAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACX,aAAa;YACb,SAAS,IAAI,KAAK,MAAM,CAAC,QAAQ,QAAQ,GAAG,MAAM;YAClD,IAAI,QACF,oCAAc,GAAG,CAAC,QAAQ;QAE9B;QACA,OAAO;IACT;IAEA,yCAAyC;IACzC,kDAAkD;IAClD,kDAAkD;IAClD,sCAAsC;IACtC,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/B,OAAO,SAAS,MAAM,YAAY;AACpC;AAEA,SAAS,mCAAa,MAAc;IAClC,6EAA6E;IAC7E,oDAAoD;IACpD,IAAI,SAAS,gCAAU;IACvB,OAAO,SAAS,CAAA,GAAA,yCAAY,CAAC,CAAC,OAAO,IAAI,IAAI;AAC/C;AAGO,SAAS,0CAAgB,IAAe,EAAE,MAAc,EAAE,cAA0B;IACzF,IAAI,OAAO,KAAK,QAAQ,CAAC,cAAc,CAAC;IACxC,OAAO,KAAK,IAAI,CAAC,AAAC,CAAA,0CAAa,0CAAa,OAAO,QAAQ,kBAAkB,IAAG,IAAK;AACvF;AAGO,SAAS,0CAAkD,CAAY,EAAE,CAAY;IAC1F,IAAI,KAAK,GACP,OAAO,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;IAGjC,OAAO,KAAK;AACd;AAGO,SAAS,0CAAkD,CAAY,EAAE,CAAY;IAC1F,IAAI,KAAK,GACP,OAAO,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;IAGjC,OAAO,KAAK;AACd;AAEA,MAAM,qCAAe;IACnB,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;AACZ;AAGO,SAAS,yCAAU,IAAe,EAAE,MAAc;IACvD,IAAI,SAAS,KAAK,QAAQ,CAAC,WAAW,CAAC;IAEvC,wEAAwE;IACxE,wCAAwC;IACxC,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,KAAK;IACxC,IAAI,YAAY,GACd,aAAa;IAGf,IAAI,SAAS,gCAAU;IACvB,uDAAuD;IACvD,oDAAoD;IACpD,IAAI,CAAC,OAAO,IAAI,GAAG,kCAAY,CAAC,OAAQ,IAAI;QAAC;QAAG;KAAE;IAClD,OAAO,cAAc,SAAS,cAAc;AAC9C;AAGO,SAAS,0CAAU,IAAe,EAAE,MAAc;IACvD,OAAO,CAAC,yCAAU,MAAM;AAC1B", "sources": ["packages/@internationalized/date/src/queries.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyTime, Calendar} from './types';\nimport {CalendarDate, CalendarDateTime, ZonedDateTime} from './CalendarDate';\nimport {fromAbsolute, toAbsolute, toCalendar, toCalendarDate} from './conversion';\nimport {weekStartData} from './weekStartData';\n\ntype DateValue = CalendarDate | CalendarDateTime | ZonedDateTime;\n\n/** Returns whether the given dates occur on the same day, regardless of the time or calendar system. */\nexport function isSameDay(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  return a.era === b.era && a.year === b.year && a.month === b.month && a.day === b.day;\n}\n\n/** Returns whether the given dates occur in the same month, using the calendar system of the first date. */\nexport function isSameMonth(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  // In the Japanese calendar, months can span multiple eras/years, so only compare the first of the month.\n  a = startOfMonth(a);\n  b = startOfMonth(b);\n  return a.era === b.era && a.year === b.year && a.month === b.month;\n}\n\n/** Returns whether the given dates occur in the same year, using the calendar system of the first date. */\nexport function isSameYear(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  a = startOfYear(a);\n  b = startOfYear(b);\n  return a.era === b.era && a.year === b.year;\n}\n\n/** Returns whether the given dates occur on the same day, and are of the same calendar system. */\nexport function isEqualDay(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameDay(a, b);\n}\n\n/** Returns whether the given dates occur in the same month, and are of the same calendar system. */\nexport function isEqualMonth(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameMonth(a, b);\n}\n\n/** Returns whether the given dates occur in the same year, and are of the same calendar system. */\nexport function isEqualYear(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameYear(a, b);\n}\n\n/** Returns whether two calendars are the same. */\nexport function isEqualCalendar(a: Calendar, b: Calendar): boolean {\n  return a.isEqual?.(b) ?? b.isEqual?.(a) ?? a.identifier === b.identifier;\n}\n\n/** Returns whether the date is today in the given time zone. */\nexport function isToday(date: DateValue, timeZone: string): boolean {\n  return isSameDay(date, today(timeZone));\n}\n\nconst DAY_MAP = {\n  sun: 0,\n  mon: 1,\n  tue: 2,\n  wed: 3,\n  thu: 4,\n  fri: 5,\n  sat: 6\n};\n\ntype DayOfWeek = 'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat';\n\n/**\n * Returns the day of week for the given date and locale. Days are numbered from zero to six,\n * where zero is the first day of the week in the given locale. For example, in the United States,\n * the first day of the week is Sunday, but in France it is Monday.\n */\nexport function getDayOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let weekStart = firstDayOfWeek ? DAY_MAP[firstDayOfWeek] : getWeekStart(locale);\n  let dayOfWeek = Math.ceil(julian + 1 - weekStart) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  return dayOfWeek;\n}\n\n/** Returns the current time in the given time zone. */\nexport function now(timeZone: string): ZonedDateTime {\n  return fromAbsolute(Date.now(), timeZone);\n}\n\n/** Returns today's date in the given time zone. */\nexport function today(timeZone: string): CalendarDate {\n  return toCalendarDate(now(timeZone));\n}\n\nexport function compareDate(a: AnyCalendarDate, b: AnyCalendarDate): number {\n  return a.calendar.toJulianDay(a) - b.calendar.toJulianDay(b);\n}\n\nexport function compareTime(a: AnyTime, b: AnyTime): number {\n  return timeToMs(a) - timeToMs(b);\n}\n\nfunction timeToMs(a: AnyTime): number {\n  return a.hour * 60 * 60 * 1000 + a.minute * 60 * 1000 + a.second * 1000 + a.millisecond;\n}\n\n/**\n * Returns the number of hours in the given date and time zone.\n * Usually this is 24, but it could be 23 or 25 if the date is on a daylight saving transition.\n */\nexport function getHoursInDay(a: CalendarDate, timeZone: string): number {\n  let ms = toAbsolute(a, timeZone);\n  let tomorrow = a.add({days: 1});\n  let tomorrowMs = toAbsolute(tomorrow, timeZone);\n  return (tomorrowMs - ms) / 3600000;\n}\n\nlet localTimeZone: string | null = null;\n\n/** Returns the time zone identifier for the current user. */\nexport function getLocalTimeZone(): string {\n  // TODO: invalidate this somehow?\n  if (localTimeZone == null) {\n    localTimeZone = new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  return localTimeZone!;\n}\n\n/** Returns the first date of the month for the given date. */\nexport function startOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function startOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function startOfMonth(date: CalendarDate): CalendarDate;\nexport function startOfMonth(date: DateValue): DateValue;\nexport function startOfMonth(date: DateValue): DateValue {\n  // Use `subtract` instead of `set` so we don't get constrained in an era.\n  return date.subtract({days: date.day - 1});\n}\n\n/** Returns the last date of the month for the given date. */\nexport function endOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function endOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function endOfMonth(date: CalendarDate): CalendarDate;\nexport function endOfMonth(date: DateValue): DateValue;\nexport function endOfMonth(date: DateValue): DateValue {\n  return date.add({days: date.calendar.getDaysInMonth(date) - date.day});\n}\n\n/** Returns the first day of the year for the given date. */\nexport function startOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function startOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function startOfYear(date: CalendarDate): CalendarDate;\nexport function startOfYear(date: DateValue): DateValue;\nexport function startOfYear(date: DateValue): DateValue {\n  return startOfMonth(date.subtract({months: date.month - 1}));\n}\n\n/** Returns the last day of the year for the given date. */\nexport function endOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function endOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function endOfYear(date: CalendarDate): CalendarDate;\nexport function endOfYear(date: DateValue): DateValue;\nexport function endOfYear(date: DateValue): DateValue {\n  return endOfMonth(date.add({months: date.calendar.getMonthsInYear(date) - date.month}));\n}\n\nexport function getMinimumMonthInYear(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumMonthInYear) {\n    return date.calendar.getMinimumMonthInYear(date);\n  }\n\n  return 1;\n}\n\nexport function getMinimumDayInMonth(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumDayInMonth) {\n    return date.calendar.getMinimumDayInMonth(date);\n  }\n\n  return 1;\n}\n\n/** Returns the first date of the week for the given date and locale. */\nexport function startOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function startOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function startOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  let dayOfWeek = getDayOfWeek(date, locale, firstDayOfWeek);\n  return date.subtract({days: dayOfWeek});\n}\n\n/** Returns the last date of the week for the given date and locale. */\nexport function endOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function endOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function endOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  return startOfWeek(date, locale, firstDayOfWeek).add({days: 6});\n}\n\nconst cachedRegions = new Map<string, string>();\n\nfunction getRegion(locale: string): string | undefined {\n  // If the Intl.Locale API is available, use it to get the region for the locale.\n  // @ts-ignore\n  if (Intl.Locale) {\n    // Constructing an Intl.Locale is expensive, so cache the result.\n    let region = cachedRegions.get(locale);\n    if (!region) {\n      // @ts-ignore\n      region = new Intl.Locale(locale).maximize().region;\n      if (region) {\n        cachedRegions.set(locale, region);\n      }\n    }\n    return region;\n  }\n\n  // If not, just try splitting the string.\n  // If the second part of the locale string is 'u',\n  // then this is a unicode extension, so ignore it.\n  // Otherwise, it should be the region.\n  let part = locale.split('-')[1];\n  return part === 'u' ? undefined : part;\n}\n\nfunction getWeekStart(locale: string): number {\n  // TODO: use Intl.Locale for this once browsers support the weekInfo property\n  // https://github.com/tc39/proposal-intl-locale-info\n  let region = getRegion(locale);\n  return region ? weekStartData[region] || 0 : 0;\n}\n\n/** Returns the number of weeks in the given month and locale. */\nexport function getWeeksInMonth(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let days = date.calendar.getDaysInMonth(date);\n  return Math.ceil((getDayOfWeek(startOfMonth(date), locale, firstDayOfWeek) + days) / 7);\n}\n\n/** Returns the lesser of the two provider dates. */\nexport function minDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) <= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\n/** Returns the greater of the two provider dates. */\nexport function maxDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) >= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\nconst WEEKEND_DATA = {\n  AF: [4, 5],\n  AE: [5, 6],\n  BH: [5, 6],\n  DZ: [5, 6],\n  EG: [5, 6],\n  IL: [5, 6],\n  IQ: [5, 6],\n  IR: [5, 5],\n  JO: [5, 6],\n  KW: [5, 6],\n  LY: [5, 6],\n  OM: [5, 6],\n  QA: [5, 6],\n  SA: [5, 6],\n  SD: [5, 6],\n  SY: [5, 6],\n  YE: [5, 6]\n};\n\n/** Returns whether the given date is on a weekend in the given locale. */\nexport function isWeekend(date: DateValue, locale: string): boolean {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let dayOfWeek = Math.ceil(julian + 1) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  let region = getRegion(locale);\n  // Use Intl.Locale for this once weekInfo is supported.\n  // https://github.com/tc39/proposal-intl-locale-info\n  let [start, end] = WEEKEND_DATA[region!] || [6, 0];\n  return dayOfWeek === start || dayOfWeek === end;\n}\n\n/** Returns whether the given date is on a weekday in the given locale. */\nexport function isWeekday(date: DateValue, locale: string): boolean {\n  return !isWeekend(date, locale);\n}\n"], "names": [], "version": 3, "file": "queries.module.js.map"}