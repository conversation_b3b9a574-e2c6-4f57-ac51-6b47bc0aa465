{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAED,6FAA6F;AAC7F,gEAAgE;AACzD,MAAM,4CAAgB;IAC3B,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAA<PERSON>;IACJ,IAAI;IACJ,IAAI;I<PERSON><PERSON>,IAA<PERSON>;<PERSON><PERSON><PERSON>,IAAI;I<PERSON><PERSON>,IAA<PERSON>;I<PERSON><PERSON>,IAA<PERSON>;<PERSON><PERSON><PERSON>,IAA<PERSON>;IACJ,IAA<PERSON>;IAC<PERSON>,IAA<PERSON>;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN", "sources": ["packages/@internationalized/date/src/weekStartData.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Data from https://github.com/unicode-cldr/cldr-core/blob/master/supplemental/weekData.json\n// Locales starting on Sunday have been removed for compression.\nexport const weekStartData = {\n  '001': 1,\n  AD: 1,\n  AE: 6,\n  AF: 6,\n  AI: 1,\n  AL: 1,\n  AM: 1,\n  AN: 1,\n  AR: 1,\n  AT: 1,\n  AU: 1,\n  AX: 1,\n  AZ: 1,\n  BA: 1,\n  BE: 1,\n  BG: 1,\n  BH: 6,\n  BM: 1,\n  BN: 1,\n  BY: 1,\n  CH: 1,\n  CL: 1,\n  CM: 1,\n  CN: 1,\n  CR: 1,\n  CY: 1,\n  CZ: 1,\n  DE: 1,\n  DJ: 6,\n  DK: 1,\n  DZ: 6,\n  EC: 1,\n  EE: 1,\n  EG: 6,\n  ES: 1,\n  FI: 1,\n  FJ: 1,\n  FO: 1,\n  FR: 1,\n  GB: 1,\n  GE: 1,\n  GF: 1,\n  GP: 1,\n  GR: 1,\n  HR: 1,\n  HU: 1,\n  IE: 1,\n  IQ: 6,\n  IR: 6,\n  IS: 1,\n  IT: 1,\n  JO: 6,\n  KG: 1,\n  KW: 6,\n  KZ: 1,\n  LB: 1,\n  LI: 1,\n  LK: 1,\n  LT: 1,\n  LU: 1,\n  LV: 1,\n  LY: 6,\n  MC: 1,\n  MD: 1,\n  ME: 1,\n  MK: 1,\n  MN: 1,\n  MQ: 1,\n  MV: 5,\n  MY: 1,\n  NL: 1,\n  NO: 1,\n  NZ: 1,\n  OM: 6,\n  PL: 1,\n  QA: 6,\n  RE: 1,\n  RO: 1,\n  RS: 1,\n  RU: 1,\n  SD: 6,\n  SE: 1,\n  SI: 1,\n  SK: 1,\n  SM: 1,\n  SY: 6,\n  TJ: 1,\n  TM: 1,\n  TR: 1,\n  UA: 1,\n  UY: 1,\n  UZ: 1,\n  VA: 1,\n  VN: 1,\n  XK: 1\n};\n"], "names": [], "version": 3, "file": "weekStartData.main.js.map"}