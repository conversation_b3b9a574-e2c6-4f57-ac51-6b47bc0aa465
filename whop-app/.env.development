# Whop App Configuration
# Copy this file to .env.local and fill in your actual values from the Whop dashboard

# Get these from your Whop app dashboard (https://whop.com/dashboard/developer/)
WHOP_API_KEY=CthixrBEA1XR4HRvWLb743FxgKTy0QvbERLgSEurzns
NEXT_PUBLIC_WHOP_APP_ID=app_zEGojMTjcpykxq
NEXT_PUBLIC_WHOP_AGENT_USER_ID=user_WC0zMOchLy5PJ
NEXT_PUBLIC_WHOP_COMPANY_ID=biz_2U7DAAZbGRWJ4K

# Server Configuration
PORT=3000
NODE_ENV=development

# Supabase Configuration
# Get these from your Supabase project dashboard (https://supabase.com/dashboard)

NEXT_PUBLIC_SUPABASE_URL=https://gdjfihhexilabozcqjmg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkamZpaGhleGlsYWJvemNxam1nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4MjY1MDYsImV4cCI6MjA2OTQwMjUwNn0.UXiDRn7GlIDCeuCyawnQWxjJIc_MA9cPcxAc9LJtNRM
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkamZpaGhleGlsYWJvemNxam1nIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzgyNjUwNiwiZXhwIjoyMDY5NDAyNTA2fQ._E4J11C8wy6jy8abrkj7jx85Ueg_P7KfiAPpYTnB5cY
