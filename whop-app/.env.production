# Production Environment Configuration
# This file should be used for production deployments

# Node Environment
NODE_ENV=production

# Server Configuration
PORT=3000
HOSTNAME=0.0.0.0

# Production Domain (set this to your actual production domain)
PRODUCTION_DOMAIN=eqxgbayetkac5w7pbnp1.apps.whop.com

# Whop App Configuration
# These should be set via your deployment platform's environment variables
# WHOP_API_KEY=your_production_api_key
# NEXT_PUBLIC_WHOP_APP_ID=your_app_id
# NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_agent_user_id
# NEXT_PUBLIC_WHOP_COMPANY_ID=your_company_id

# Database Configuration
# Using Supabase for production (recommended)
# Set these via your deployment platform's environment variables:
# NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
# SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# For custom PostgreSQL (alternative)
# DATABASE_URL=postgresql://username:password@host:port/database

# Logging
LOG_LEVEL=info
