# Deployment Guide

## Frontend-Only Architecture

This Whiteboard app now uses a **frontend-only architecture** with Supabase for database and real-time functionality. This means it can be deployed on any Next.js-compatible platform without needing custom server support.

## Supported Platforms

✅ **All platforms now supported:**
- **Vercel** (Recommended)
- **Netlify**
- **Railway**
- **Render**
- **DigitalOcean App Platform**
- **AWS Amplify**
- **Any static hosting with Next.js support**

## Environment Variables

Set these environment variables in your deployment platform:

```bash
# Whop Configuration
WHOP_API_KEY=your_whop_api_key
NEXT_PUBLIC_WHOP_APP_ID=your_app_id
NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_agent_user_id
NEXT_PUBLIC_WHOP_COMPANY_ID=your_company_id

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_supabase_anon_key
```

## Database Setup

1. **Create Supabase Project**: Go to [supabase.com](https://supabase.com) and create a new project
2. **Run Database Schema**: Copy the SQL from `supabase-schema.sql` and run it in your Supabase SQL Editor
3. **Get Environment Variables**: Copy your project URL and anon key from the Supabase dashboard

## Deployment Steps

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Netlify

1. Push your code to GitHub
2. Connect your repository to Netlify
3. Set build command: `npm run build`
4. Set publish directory: `.next`
5. Add environment variables in Netlify dashboard

### Railway

1. Connect your GitHub repository
2. Add environment variables
3. Deploy with automatic builds

## Post-Deployment

1. **Update Whop App Settings**:
   - Set your Base URL to your deployment domain
   - Ensure the app path is set to `/experiences/[experienceId]`

2. **Test the Application**:
   - Create a test session
   - Verify real-time collaboration works
   - Check that drawing data persists

## Troubleshooting

### Common Issues

**Supabase Connection Failed**
- Verify environment variables are set correctly
- Check that your Supabase project is active
- Ensure database tables are created

**App Not Loading in Whop**
- Confirm Base URL matches your deployment domain
- Verify app is installed in your Whop community
- Check browser console for errors

**Real-time Not Working**
- Verify Supabase Realtime is enabled in your project
- Check browser console for WebSocket errors
- Ensure multiple users are in the same session

## Benefits of New Architecture

- ✅ **Universal Deployment**: Works on any platform
- ✅ **No Server Management**: Fully serverless
- ✅ **Better Performance**: Direct database connections
- ✅ **Easier Scaling**: Leverages Supabase infrastructure
- ✅ **Simplified Maintenance**: Fewer moving parts
