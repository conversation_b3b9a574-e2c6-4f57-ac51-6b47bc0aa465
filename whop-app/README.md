# Collaborative Whiteboard - Whop App

A real-time collaborative whiteboard application built for Whop communities. This app allows multiple users to draw, sketch, and create together on a shared canvas with real-time synchronization.

## Features

### 🎨 Drawing Tools
- **Pen Tool**: Freehand drawing with customizable colors and brush sizes
- **Eraser**: Remove parts of drawings
- **Shapes**: Rectangle, circle, and line tools
- **Text Tool**: Add text annotations with different fonts and sizes
- **Color Picker**: Wide range of colors for drawing and shapes
- **Brush Properties**: Adjustable stroke width, opacity, and fill options

### 🤝 Real-time Collaboration
- **Live Drawing Sync**: See other users' drawings in real-time
- **User Cursors**: View other users' cursor positions and names
- **User Presence**: See who's currently active on the whiteboard
- **Session Management**: Create and join different whiteboard sessions

### 💾 Persistence & Management
- **Save/Load**: Persistent storage of whiteboard sessions
- **Undo/Redo**: Full history management with undo/redo functionality
- **Session Scoping**: Whiteboards are scoped to specific Whop experiences
- **Access Control**: Leverages Who<PERSON>'s user roles (admin/customer)

### 📱 User Experience
- **Responsive Design**: Works on desktop and mobile devices
- **Touch Support**: Mobile-friendly drawing interface
- **Intuitive UI**: Clean toolbar and easy-to-use controls
- **Performance Optimized**: Efficient canvas rendering and data transmission

## Setup Instructions

### 1. Install Dependencies

```bash
npm install --legacy-peer-deps
```

### 2. Create a Whop App

1. Go to your [Whop Developer Dashboard](https://whop.com/dashboard/developer/)
2. Create a new app
3. In the "Hosting" section, configure:
   - **Base URL**: `http://localhost:3000/` (for development)
   - **App path**: `/experiences/[experienceId]`
   - **Discover path**: `/discover`

### 3. Environment Variables

1. Copy `.env.local.example` to `.env.local`
2. Fill in your Whop app credentials from the dashboard:

```env
WHOP_API_KEY="your_whop_api_key_here"
WHOP_WEBHOOK_SECRET="your_webhook_secret_here"
NEXT_PUBLIC_WHOP_AGENT_USER_ID="your_agent_user_id_here"
NEXT_PUBLIC_WHOP_APP_ID="your_app_id_here"
NEXT_PUBLIC_WHOP_COMPANY_ID="your_company_id_here"
```

### 4. Run the Development Server

**⚠️ IMPORTANT: Always use the Whop proxy for development!**

```bash
npm run dev
```

This command runs `whop-proxy --command 'next dev --turbopack'` which:
- Starts the Whop development proxy
- Injects user authentication tokens
- Enables iframe integration with Whop
- Starts the Next.js development server

The app will start on `http://localhost:3000` with Supabase Realtime for real-time collaboration.

**Alternative Commands:**
- `npm run dev:no-proxy` - Run without proxy (will cause authentication errors)
- `pnpm dev` - If using pnpm package manager

### 5. Install the App

1. Go to a Whop community in the same organization as your app
2. Navigate to the tools section and add your app
3. Use the dev toggle in the top right to test locally

## Technical Architecture

### Frontend-Only Architecture
- **Next.js 15**: App Router with frontend-only components
- **Supabase**: Database and real-time functionality
- **Supabase Realtime**: Real-time collaboration and presence
- **Whop SDK**: Authentication and access control integration

### Frontend
- **React 19**: Modern React with hooks and context
- **HTML5 Canvas**: High-performance drawing interface
- **Tailwind CSS**: Utility-first styling
- **Lucide Icons**: Beautiful, consistent icons

### Real-time Features
- **Supabase Realtime**: Native real-time collaboration
- **Event-driven Architecture**: Efficient message passing for drawing actions
- **Conflict Resolution**: Handles simultaneous drawing operations
- **Presence System**: User awareness and cursor tracking

## Development

### Project Structure

```
whop-app/
├── app/
│   ├── experiences/[experienceId]/ # Main whiteboard experience
│   └── layout.tsx               # Root layout with Whop integration
├── components/
│   ├── Whiteboard.tsx           # Main whiteboard component
│   ├── Canvas.tsx               # Drawing canvas implementation
│   ├── Toolbar.tsx              # Drawing tools and properties
│   ├── UserList.tsx             # Active users display
│   ├── SessionManager.tsx       # Session creation and management
│   └── WhiteboardApp.tsx        # App orchestration
├── lib/
│   ├── supabase.ts              # Supabase client configuration
│   ├── supabase-sessions.ts     # Session management with Supabase
│   ├── supabase-realtime.ts     # Real-time collaboration
│   ├── types.ts                 # TypeScript definitions
│   └── whop-sdk.ts              # Whop SDK configuration
└── supabase-schema.sql          # Database schema for Supabase
```

### Key Components

- **WhiteboardApp**: Main app component that handles session management
- **Whiteboard**: Core whiteboard interface with tools and canvas
- **Canvas**: HTML5 canvas implementation with drawing logic
- **Toolbar**: Tool selection and property controls
- **SessionManager**: Create, join, and manage whiteboard sessions

## Deployment

### Vercel Deployment (Recommended)

1. Push your code to GitHub
2. Connect to Vercel and deploy
3. Add environment variables in Vercel dashboard
4. Update your Whop app's Base URL to your Vercel domain

### Other Platforms

The app now works on any Next.js-compatible platform:

1. **Vercel**, **Netlify**, **Railway**, **Render**, or **DigitalOcean**
2. Set `NODE_ENV=production`
3. Run `npm run build && npm start`

## Troubleshooting

### Common Issues

**Supabase Connection Failed**
- Verify your Supabase environment variables are set correctly
- Check that your Supabase project is active
- Ensure the database tables are created (run the schema from `supabase-schema.sql`)

**App Not Loading in Whop**
- Confirm the App path is set to `/experiences/[experienceId]`
- Verify environment variables are correctly set
- Check that the app is installed in your Whop community

**Drawing Not Syncing**
- Check Supabase Realtime connection status
- Verify multiple users are in the same session
- Check browser console for Supabase errors

### Database Setup

The app uses Supabase for the database. Run the SQL schema from `supabase-schema.sql` in your Supabase dashboard to create the required tables.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly with multiple users
5. Submit a pull request

## License

This project is built on the Whop NextJS App Template and follows the same licensing terms.

## Support

For Whop-specific issues, visit the [Whop Documentation](https://dev.whop.com/introduction).

For app-specific issues, check the troubleshooting section above or create an issue in the repository.
