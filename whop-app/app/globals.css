@import "@whop/react/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden; /* Prevent page scrolling */
  height: 100vh;
  margin: 0;
  padding: 0;
}

html {
  overflow: hidden; /* Prevent page scrolling */
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Override Frosted UI colors for better contrast */
@layer base {
  :root {
    --foreground: #1a1a1a;
    --background: #ffffff;
  }

  /* Force better contrast for text elements */
  .frosted-ui {
    --gray-12: #1a1a1a !important;
    --gray-11: #2d2d2d !important;
    --gray-10: #404040 !important;
    --gray-9: #6b7280 !important;
    --gray-8: #9ca3af !important;
    --gray-7: #d1d5db !important;
    --gray-6: #e5e7eb !important;
  }
}

/* Custom utilities for better UX */
@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .shadow-xl {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  /* High contrast text utilities */
  .text-high-contrast {
    color: #1a1a1a !important;
  }

  .text-medium-contrast {
    color: #374151 !important;
  }

  .text-low-contrast {
    color: #6b7280 !important;
  }
}

/* Component-specific overrides */
@layer components {
  /* Force better contrast for all Frosted UI text elements */
  .frosted-ui,
  .frosted-ui * {
    --gray-12: #1a1a1a !important;
    --gray-11: #2d2d2d !important;
    --gray-10: #404040 !important;
    --gray-9: #6b7280 !important;
  }

  /* Force better contrast for headings */
  .frosted-ui h1,
  .frosted-ui h2,
  .frosted-ui h3,
  .frosted-ui h4,
  .frosted-ui h5,
  .frosted-ui h6,
  .frosted-ui [data-accent-color],
  .frosted-ui [data-radix-collection-item] {
    color: #1a1a1a !important;
  }

  /* Better contrast for text elements */
  .frosted-ui p,
  .frosted-ui span,
  .frosted-ui div,
  .frosted-ui [role="heading"],
  .frosted-ui [data-radix-collection-item] {
    color: #374151 !important;
  }

  /* Ensure buttons have proper contrast */
  .frosted-ui button,
  .frosted-ui [role="button"] {
    color: #1a1a1a !important;
  }

  /* Gray text should still be readable */
  .frosted-ui [data-accent-color="gray"] {
    color: #6b7280 !important;
  }

  /* Specific overrides for common Frosted UI components */
  .frosted-ui [data-radix-collection-item],
  .frosted-ui [data-radix-select-content],
  .frosted-ui [data-radix-dialog-content] {
    color: #1a1a1a !important;
  }

  /* Ensure all text in cards is visible */
  .frosted-ui [data-radix-card],
  .frosted-ui .card {
    color: #1a1a1a !important;
  }

  /* Override any remaining light text */
  .frosted-ui [style*="color"] {
    color: #1a1a1a !important;
  }

  /* Custom button styles for home page */
  .home-button {
    color: white !important;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
  }

  .home-button:hover {
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .home-button:active {
    transform: translateY(0);
  }

  /* Session Manager button styles - ensure white text */
  .fui-Button[style*="background"] {
    color: white !important;
  }

  .fui-Button[style*="background"] span {
    color: white !important;
  }

  .fui-Button[style*="gradient"] {
    color: white !important;
  }

  .fui-Button[style*="gradient"] span {
    color: white !important;
  }

  /* Force white text on gradient buttons */
  button[class*="gradient"] {
    color: white !important;
  }

  button[class*="gradient"] span {
    color: white !important;
  }
}

/* Global text visibility fixes */
@layer base {
  /* Ensure all text is visible by default */
  * {
    color: inherit;
  }

  /* Force dark text on light backgrounds */
  body,
  html {
    color: #1a1a1a;
  }

  /* Override any extremely light text */
  [style*="color: rgb(255, 255, 255)"],
  [style*="color: white"],
  [style*="color: #fff"],
  [style*="color: #ffffff"] {
    color: #1a1a1a !important;
  }
}

/* Animated background blobs */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Prevent canvas scrolling */
canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

/* Ensure no scrollbars appear */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Smooth transitions for interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
