'use client';

import React from 'react';
import { WhiteboardWrapper } from '@/components/WhiteboardWrapper';

// Test page for development - bypasses Whop authentication
export default function TestPage() {
  // Mock data for testing
  const mockProps = {
    experienceId: 'test-experience-123',
    userId: 'test-user-456',
    username: 'TestUser',
    accessLevel: 'admin' as const
  };

  return (
    <div className="h-screen">
      <div className="bg-yellow-100 border-b border-yellow-300 px-4 py-2 text-sm text-yellow-800">
        <strong>Test Mode:</strong> This is a test page for development. 
        In production, users will access the whiteboard through Whop experiences.
      </div>
      
      <div className="h-[calc(100vh-48px)]">
        <WhiteboardWrapper {...mockProps} />
      </div>
    </div>
  );
}
