'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { DrawingTool, ToolProperties, DrawingElement, UserCursor, CanvasState, Point, DrawingPath, Shape, TextElement, ViewportState, SelectionState, CanvasProps } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';

// Check if we're in the browser
const isBrowser = typeof window !== 'undefined';



export function Canvas({
  sessionId,
  userId,
  username,
  currentTool,
  toolProperties,
  canvasState,
  activeUsers,
  onDrawingAction,
  onCursorMove,
  onZoom,
  onPan,
  onSelectionChange
}: CanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isPanning, setIsPanning] = useState(false);
  const [currentPath, setCurrentPath] = useState<Point[]>([]);
  const [startPoint, setStartPoint] = useState<Point | null>(null);
  const [currentElement, setCurrentElement] = useState<DrawingElement | null>(null);
  const [lastPanPoint, setLastPanPoint] = useState<Point | null>(null);
  const [selectionState, setSelectionState] = useState<SelectionState>({
    selectedElements: [],
    isDragging: false
  });
  const [activeTextElement, setActiveTextElement] = useState<{
    id: string;
    position: Point;
    text: string;
    isEditing: boolean;
    cursorPosition: number;
  } | null>(null);

  // Coordinate transformation functions
  const screenToCanvas = useCallback((screenX: number, screenY: number): Point => {
    // Safety check: ensure viewport exists
    if (!canvasState.viewport) {
      return { x: screenX, y: screenY };
    }
    const { zoom, panX, panY } = canvasState.viewport;
    return {
      x: (screenX - panX) / zoom,
      y: (screenY - panY) / zoom
    };
  }, [canvasState.viewport]);

  const canvasToScreen = useCallback((canvasX: number, canvasY: number): Point => {
    // Safety check: ensure viewport exists
    if (!canvasState.viewport) {
      return { x: canvasX, y: canvasY };
    }
    const { zoom, panX, panY } = canvasState.viewport;
    return {
      x: canvasX * zoom + panX,
      y: canvasY * zoom + panY
    };
  }, [canvasState.viewport]);

  // Get mouse position relative to canvas
  const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>): Point => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const screenX = e.clientX - rect.left;
    const screenY = e.clientY - rect.top;

    return screenToCanvas(screenX, screenY);
  };

  // Get raw screen position (for panning)
  const getScreenPos = (e: React.MouseEvent<HTMLCanvasElement>): Point => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  // Check if a point intersects with an element
  const isPointInElement = (point: Point, element: DrawingElement): boolean => {
    if ('points' in element) {
      // For drawing paths, check if point is close to any line segment
      const path = element as DrawingPath;
      const threshold = Math.max(path.strokeWidth, 5);

      for (let i = 0; i < path.points.length - 1; i++) {
        const p1 = path.points[i];
        const p2 = path.points[i + 1];
        const distance = distanceToLineSegment(point, p1, p2);
        if (distance <= threshold) return true;
      }
      return false;
    } else if ('startPoint' in element) {
      // For shapes
      const shape = element as Shape;
      const { startPoint, endPoint } = shape;
      const minX = Math.min(startPoint.x, endPoint.x);
      const maxX = Math.max(startPoint.x, endPoint.x);
      const minY = Math.min(startPoint.y, endPoint.y);
      const maxY = Math.max(startPoint.y, endPoint.y);

      if (shape.type === 'rectangle') {
        return point.x >= minX && point.x <= maxX && point.y >= minY && point.y <= maxY;
      } else if (shape.type === 'circle') {
        const centerX = (startPoint.x + endPoint.x) / 2;
        const centerY = (startPoint.y + endPoint.y) / 2;
        const radius = Math.sqrt(Math.pow(endPoint.x - startPoint.x, 2) + Math.pow(endPoint.y - startPoint.y, 2)) / 2;
        const distance = Math.sqrt(Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2));
        return distance <= radius;
      } else if (shape.type === 'line') {
        const distance = distanceToLineSegment(point, startPoint, endPoint);
        return distance <= Math.max(shape.strokeWidth, 5);
      }
    } else if ('position' in element) {
      // For text elements
      const text = element as TextElement;
      const textWidth = text.text.length * (text.fontSize * 0.6); // Approximate text width
      const textHeight = text.fontSize;
      return point.x >= text.position.x && point.x <= text.position.x + textWidth &&
             point.y >= text.position.y - textHeight && point.y <= text.position.y;
    }
    return false;
  };

  // Calculate distance from point to line segment
  const distanceToLineSegment = (point: Point, lineStart: Point, lineEnd: Point): number => {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    if (lenSq !== 0) {
      param = dot / lenSq;
    }

    let xx, yy;
    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Get elements that intersect with eraser path
  const getElementsIntersectingPath = (path: Point[]): string[] => {
    const intersectingIds: string[] = [];
    const eraserRadius = Math.max(toolProperties.strokeWidth / 2, 5);

    canvasState.elements.forEach(element => {
      let intersects = false;

      // Check multiple points along the eraser path for better detection
      for (let i = 0; i < path.length; i++) {
        const point = path[i];

        // Create a small circle around each eraser point
        const testPoints = [
          point,
          { x: point.x - eraserRadius, y: point.y },
          { x: point.x + eraserRadius, y: point.y },
          { x: point.x, y: point.y - eraserRadius },
          { x: point.x, y: point.y + eraserRadius }
        ];

        for (const testPoint of testPoints) {
          if (isPointInElement(testPoint, element)) {
            intersects = true;
            break;
          }
        }

        if (intersects) break;
      }

      if (intersects) {
        intersectingIds.push(element.id);
      }
    });

    return intersectingIds;
  };

  // Draw grid background for infinite canvas feel
  const drawGrid = useCallback((ctx: CanvasRenderingContext2D) => {
    const canvas = canvasRef.current;
    if (!canvas || !canvasState.viewport) return;

    const { zoom, panX, panY } = canvasState.viewport;
    const gridSize = 20 * zoom;

    if (gridSize < 5) return; // Don't draw grid when too zoomed out

    ctx.save();
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.5;

    // Calculate grid offset
    const offsetX = panX % gridSize;
    const offsetY = panY % gridSize;

    // Draw vertical lines
    for (let x = offsetX; x < canvas.width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }

    // Draw horizontal lines
    for (let y = offsetY; y < canvas.height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    ctx.restore();
  }, [canvasState.viewport]);

  // Redraw entire canvas
  const redrawCanvas = useCallback((ctx: CanvasRenderingContext2D) => {
    const canvas = canvasRef.current;
    if (!canvas || !canvasState.viewport) return;

    // Clear canvas
    ctx.fillStyle = canvasState.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    drawGrid(ctx);

    // Apply viewport transformation
    ctx.save();
    const { zoom, panX, panY } = canvasState.viewport;
    ctx.scale(zoom, zoom);
    ctx.translate(panX / zoom, panY / zoom);

    // Draw all elements
    canvasState.elements.forEach(element => {
      drawElement(ctx, element);
    });

    // Draw active text element being edited directly on canvas
    if (activeTextElement && currentTool === 'text') {
      ctx.save();
      ctx.fillStyle = toolProperties.color;
      ctx.font = `${toolProperties.fontSize}px ${toolProperties.fontFamily}`;
      ctx.textBaseline = 'top';
      ctx.globalAlpha = toolProperties.opacity;

      // Draw the text
      const text = activeTextElement.text;
      ctx.fillText(text, activeTextElement.position.x, activeTextElement.position.y);

      // Draw blinking cursor only when text tool is active and element is still active
      const shouldShowCursor = activeTextElement && currentTool === 'text' && Math.floor(Date.now() / 500) % 2 === 0;
      if (shouldShowCursor) {
        const textBeforeCursor = text.substring(0, activeTextElement.cursorPosition);
        const textMetrics = ctx.measureText(textBeforeCursor);
        const cursorX = activeTextElement.position.x + textMetrics.width;
        const cursorY = activeTextElement.position.y;

        ctx.strokeStyle = toolProperties.color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(cursorX, cursorY);
        ctx.lineTo(cursorX, cursorY + toolProperties.fontSize);
        ctx.stroke();
      }

      ctx.restore();
    }

    // Selection indicators removed as requested

    ctx.restore();

    // Draw active users' cursors (in screen space)
    activeUsers.forEach(user => {
      const screenPos = canvasToScreen(user.x, user.y);
      drawUserCursor(ctx, { ...user, x: screenPos.x, y: screenPos.y });
    });
  }, [canvasState, activeUsers, drawGrid, canvasToScreen, activeTextElement, toolProperties]);

  // Initialize canvas
  useEffect(() => {
    if (!isBrowser) return;

    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to container size
    const rect = container.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Clear and redraw
    redrawCanvas(ctx);
  }, [canvasState, redrawCanvas]);

  // Handle window resize
  useEffect(() => {
    if (!isBrowser) return;

    const handleResize = () => {
      const canvas = canvasRef.current;
      const container = containerRef.current;
      if (!canvas || !container) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const rect = container.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;

      redrawCanvas(ctx);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [redrawCanvas]);

  // Draw individual element
  const drawElement = (ctx: CanvasRenderingContext2D, element: DrawingElement) => {
    ctx.save();
    ctx.globalAlpha = element.opacity || 1;

    if ('points' in element) {
      // Drawing path
      const path = element as DrawingPath;
      if (path.tool === 'eraser') {
        ctx.globalCompositeOperation = 'destination-out';
      } else {
        ctx.globalCompositeOperation = 'source-over';
      }
      
      ctx.strokeStyle = path.color;
      ctx.lineWidth = path.strokeWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      if (path.points.length > 1) {
        ctx.beginPath();
        ctx.moveTo(path.points[0].x, path.points[0].y);
        for (let i = 1; i < path.points.length; i++) {
          ctx.lineTo(path.points[i].x, path.points[i].y);
        }
        ctx.stroke();
      }
    } else if ('startPoint' in element) {
      // Shape
      const shape = element as Shape;
      ctx.strokeStyle = shape.color;
      ctx.lineWidth = shape.strokeWidth;

      if (shape.filled && shape.fillColor) {
        ctx.fillStyle = shape.fillColor;
      }

      const width = shape.endPoint.x - shape.startPoint.x;
      const height = shape.endPoint.y - shape.startPoint.y;

      ctx.beginPath();
      if (shape.type === 'rectangle') {
        ctx.rect(shape.startPoint.x, shape.startPoint.y, width, height);
      } else if (shape.type === 'circle') {
        const centerX = shape.startPoint.x + width / 2;
        const centerY = shape.startPoint.y + height / 2;
        const radius = Math.sqrt(width * width + height * height) / 2;
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      } else if (shape.type === 'line') {
        ctx.moveTo(shape.startPoint.x, shape.startPoint.y);
        ctx.lineTo(shape.endPoint.x, shape.endPoint.y);
      }

      if (shape.filled && shape.fillColor) {
        ctx.fill();
      }
      ctx.stroke();
    } else if ('position' in element && 'text' in element) {
      // Text element
      const textEl = element as TextElement;
      ctx.fillStyle = textEl.color;
      ctx.font = `${textEl.fontSize}px ${textEl.fontFamily}`;
      ctx.textBaseline = 'top'; // Ensure consistent text positioning
      ctx.fillText(textEl.text, textEl.position.x, textEl.position.y);
    }

    ctx.restore();
  };

  // Draw user cursor
  const drawUserCursor = (ctx: CanvasRenderingContext2D, user: UserCursor) => {
    ctx.save();
    ctx.fillStyle = user.color;
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;

    // Draw cursor circle
    ctx.beginPath();
    ctx.arc(user.x, user.y, 8, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Draw username
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.fillText(user.username, user.x + 12, user.y - 8);

    ctx.restore();
  };

  // Handle mouse down
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    // CRITICAL: If we're not in text mode, save and clear any active text element
    if (currentTool !== 'text' && activeTextElement) {
      // Save the text if there's any content before clearing
      if (activeTextElement.text.trim()) {
        const element: TextElement = {
          id: activeTextElement.id,
          position: activeTextElement.position,
          text: activeTextElement.text.trim(),
          fontSize: toolProperties.fontSize,
          color: toolProperties.color,
          fontFamily: toolProperties.fontFamily,
          opacity: toolProperties.opacity,
          timestamp: Date.now(),
          userId
        };
        onDrawingAction(element, 'add');
      }
      setActiveTextElement(null);
      return; // Exit early to prevent other tool actions until next click
    }

    if (currentTool === 'text') {
      // If there's already an active text element, finish it first
      if (activeTextElement) {
        handleTextSubmit();
      }
      const pos = getMousePos(e);
      handleTextClick(e);
      return;
    }

    if (currentTool === 'pan' || e.button === 1) { // Middle mouse button or pan tool
      setIsPanning(true);
      setLastPanPoint(getScreenPos(e)); // Use screen coordinates for panning
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    const pos = getMousePos(e);

    if (currentTool === 'select') {
      // Handle selection
      const clickedElement = canvasState.elements.find(element => isPointInElement(pos, element));

      if (clickedElement) {
        if (e.shiftKey) {
          // Add to selection
          const newSelection = selectionState.selectedElements.includes(clickedElement.id)
            ? selectionState.selectedElements.filter(id => id !== clickedElement.id)
            : [...selectionState.selectedElements, clickedElement.id];
          setSelectionState(prev => ({ ...prev, selectedElements: newSelection }));
          onSelectionChange?.(newSelection);
        } else {
          // Single selection
          setSelectionState(prev => ({
            ...prev,
            selectedElements: [clickedElement.id],
            isDragging: true,
            dragOffset: { x: pos.x, y: pos.y }
          }));
          onSelectionChange?.([clickedElement.id]);
        }
      } else {
        // Clear selection if clicking empty space
        setSelectionState(prev => ({ ...prev, selectedElements: [] }));
        onSelectionChange?.([]);
      }
      return;
    }

    setIsDrawing(true);
    setStartPoint(pos);

    if (currentTool === 'pen' || currentTool === 'eraser') {
      setCurrentPath([pos]);
    }
  };

  // Handle mouse move
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const pos = getMousePos(e);

    // Always emit cursor position
    onCursorMove(pos.x, pos.y);

    // Handle panning
    if (isPanning && lastPanPoint) {
      e.preventDefault();
      e.stopPropagation();

      const screenPos = getScreenPos(e); // Use screen coordinates for panning
      const deltaX = screenPos.x - lastPanPoint.x;
      const deltaY = screenPos.y - lastPanPoint.y;

      // Only update if there's actual movement
      if (Math.abs(deltaX) > 0.1 || Math.abs(deltaY) > 0.1) {
        onPan(deltaX, deltaY);
        setLastPanPoint(screenPos);
      }

      return;
    }

    // Handle selection dragging
    if (currentTool === 'select' && selectionState.isDragging && selectionState.dragOffset) {
      const deltaX = pos.x - selectionState.dragOffset.x;
      const deltaY = pos.y - selectionState.dragOffset.y;

      // Move selected elements
      selectionState.selectedElements.forEach(elementId => {
        const element = canvasState.elements.find(el => el.id === elementId);
        if (element) {
          let updatedElement: DrawingElement;

          if ('points' in element) {
            // Move drawing path
            const path = element as DrawingPath;
            updatedElement = {
              ...path,
              points: path.points.map(point => ({
                x: point.x + deltaX,
                y: point.y + deltaY
              }))
            };
          } else if ('startPoint' in element) {
            // Move shape
            const shape = element as Shape;
            updatedElement = {
              ...shape,
              startPoint: {
                x: shape.startPoint.x + deltaX,
                y: shape.startPoint.y + deltaY
              },
              endPoint: {
                x: shape.endPoint.x + deltaX,
                y: shape.endPoint.y + deltaY
              }
            };
          } else if ('position' in element) {
            // Move text
            const text = element as TextElement;
            updatedElement = {
              ...text,
              position: {
                x: text.position.x + deltaX,
                y: text.position.y + deltaY
              }
            };
          } else {
            return;
          }

          onDrawingAction(updatedElement, 'update');
        }
      });

      setSelectionState(prev => ({
        ...prev,
        dragOffset: pos
      }));
      return;
    }

    if (!isDrawing || !startPoint) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    if (currentTool === 'pen' || currentTool === 'eraser') {
      // Add point to current path
      setCurrentPath(prev => [...prev, pos]);

      // Draw preview
      redrawCanvas(ctx);

      // Draw current path with viewport transformation
      if (currentPath.length > 0 && canvasState.viewport) {
        ctx.save();

        // Apply viewport transformation for preview
        const { zoom, panX, panY } = canvasState.viewport;
        ctx.scale(zoom, zoom);
        ctx.translate(panX / zoom, panY / zoom);

        if (currentTool === 'eraser') {
          // Show eraser path as a visible red line for feedback
          ctx.strokeStyle = '#ff0000';
          ctx.globalAlpha = 0.5;
        } else {
          ctx.strokeStyle = toolProperties.color;
          ctx.globalAlpha = toolProperties.opacity;
        }

        ctx.lineWidth = toolProperties.strokeWidth;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        ctx.beginPath();
        ctx.moveTo(currentPath[0].x, currentPath[0].y);
        currentPath.forEach(point => {
          ctx.lineTo(point.x, point.y);
        });
        ctx.lineTo(pos.x, pos.y);
        ctx.stroke();
        ctx.restore();
      }
    } else if (['rectangle', 'circle', 'line'].includes(currentTool)) {
      // Draw shape preview
      redrawCanvas(ctx);

      if (!canvasState.viewport) return;

      ctx.save();

      // Apply viewport transformation for preview
      const { zoom, panX, panY } = canvasState.viewport;
      ctx.scale(zoom, zoom);
      ctx.translate(panX / zoom, panY / zoom);

      ctx.strokeStyle = toolProperties.color;
      ctx.lineWidth = toolProperties.strokeWidth;
      ctx.globalAlpha = toolProperties.opacity;

      if (toolProperties.filled) {
        ctx.fillStyle = toolProperties.fillColor;
      }

      const width = pos.x - startPoint.x;
      const height = pos.y - startPoint.y;

      ctx.beginPath();
      if (currentTool === 'rectangle') {
        ctx.rect(startPoint.x, startPoint.y, width, height);
      } else if (currentTool === 'circle') {
        const centerX = startPoint.x + width / 2;
        const centerY = startPoint.y + height / 2;
        const radius = Math.sqrt(width * width + height * height) / 2;
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      } else if (currentTool === 'line') {
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(pos.x, pos.y);
      }

      if (toolProperties.filled) {
        ctx.fill();
      }
      ctx.stroke();
      ctx.restore();
    }
  };

  // Handle mouse up
  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (isPanning) {
      setIsPanning(false);
      setLastPanPoint(null);
      return;
    }

    // Handle selection dragging end
    if (currentTool === 'select' && selectionState.isDragging) {
      setSelectionState(prev => ({
        ...prev,
        isDragging: false,
        dragOffset: undefined
      }));
      return;
    }

    if (!isDrawing || !startPoint) return;

    const pos = getMousePos(e);
    setIsDrawing(false);

    let element: DrawingElement | null = null;

    if (currentTool === 'pen') {
      if (currentPath.length > 0) {
        element = {
          id: uuidv4(),
          tool: currentTool,
          points: [...currentPath, pos],
          color: toolProperties.color,
          strokeWidth: toolProperties.strokeWidth,
          opacity: toolProperties.opacity,
          timestamp: Date.now(),
          userId
        } as DrawingPath;
      }
    } else if (currentTool === 'eraser') {
      // For eraser, remove intersecting elements instead of creating a path
      if (currentPath.length > 0) {
        const eraserPath = [...currentPath, pos];
        const elementsToRemove = getElementsIntersectingPath(eraserPath);

        // Remove each intersecting element
        elementsToRemove.forEach(elementId => {
          const elementToRemove = canvasState.elements.find(el => el.id === elementId);
          if (elementToRemove) {
            onDrawingAction(elementToRemove, 'remove');
          }
        });
      }
    } else if (['rectangle', 'circle', 'line'].includes(currentTool)) {
      element = {
        id: uuidv4(),
        type: currentTool as 'rectangle' | 'circle' | 'line',
        startPoint,
        endPoint: pos,
        color: toolProperties.color,
        strokeWidth: toolProperties.strokeWidth,
        opacity: toolProperties.opacity,
        filled: toolProperties.filled,
        fillColor: toolProperties.fillColor,
        timestamp: Date.now(),
        userId
      } as Shape;
    }

    if (element) {
      onDrawingAction(element, 'add');
    }

    setCurrentPath([]);
    setStartPoint(null);
    setCurrentElement(null);
  };

  // Handle text input
  const handleTextClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (currentTool === 'text') {
      const pos = getMousePos(e);

      // Check if clicking on existing text to edit it
      const clickedTextElement = canvasState.elements.find(element => {
        if ('position' in element && 'text' in element) {
          const textEl = element as TextElement;
          const textWidth = textEl.text.length * (textEl.fontSize * 0.6);
          const textHeight = textEl.fontSize;
          return pos.x >= textEl.position.x &&
                 pos.x <= textEl.position.x + textWidth &&
                 pos.y >= textEl.position.y &&
                 pos.y <= textEl.position.y + textHeight;
        }
        return false;
      });

      if (clickedTextElement) {
        // Edit existing text
        const textEl = clickedTextElement as TextElement;
        setActiveTextElement({
          id: textEl.id,
          position: textEl.position,
          text: textEl.text,
          isEditing: true,
          cursorPosition: textEl.text.length
        });
        // Remove the existing element temporarily while editing
        onDrawingAction(textEl, 'remove');
      } else {
        // Create new text
        if (activeTextElement) {
          handleTextSubmit();
        }

        const newTextElement = {
          id: uuidv4(),
          position: pos,
          text: '',
          isEditing: true,
          cursorPosition: 0
        };
        setActiveTextElement(newTextElement);
      }
    }
  };

  // Handle text submission
  const handleTextSubmit = () => {
    if (activeTextElement) {
      if (activeTextElement.text.trim()) {
        const element: TextElement = {
          id: activeTextElement.id,
          position: activeTextElement.position,
          text: activeTextElement.text.trim(),
          fontSize: toolProperties.fontSize,
          color: toolProperties.color,
          fontFamily: toolProperties.fontFamily,
          opacity: toolProperties.opacity,
          timestamp: Date.now(),
          userId
        };
        onDrawingAction(element, 'add');
      }
      setActiveTextElement(null);
    }
  };

  // Handle text cancel
  const handleTextCancel = () => {
    setActiveTextElement(null);
  };

  // No keyboard handling needed - the HTML input handles it

  // CRITICAL: Save and clear text element when tool changes - this must be immediate and synchronous
  useEffect(() => {
    if (currentTool !== 'text' && activeTextElement) {
      // Save the text if there's any content before clearing
      if (activeTextElement.text.trim()) {
        const element: TextElement = {
          id: activeTextElement.id,
          position: activeTextElement.position,
          text: activeTextElement.text.trim(),
          fontSize: toolProperties.fontSize,
          color: toolProperties.color,
          fontFamily: toolProperties.fontFamily,
          opacity: toolProperties.opacity,
          timestamp: Date.now(),
          userId
        };
        onDrawingAction(element, 'add');
      }

      // Then immediately clear the active text element
      setActiveTextElement(null);
    }
  }, [currentTool, activeTextElement, toolProperties, onDrawingAction, userId]);

  // Handle keyboard input - ONLY when text tool is active and we have an active text element
  useEffect(() => {
    // Only add keyboard listener when text tool is active and we have an active text element
    if (currentTool !== 'text' || !activeTextElement) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // Double check we're still in text mode with active element
      if (currentTool !== 'text' || !activeTextElement) {
        return;
      }

      if (e.key === 'Enter') {
        e.preventDefault();
        handleTextSubmit();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        setActiveTextElement(null);
      } else if (e.key === 'Backspace') {
        e.preventDefault();
        if (activeTextElement.cursorPosition > 0) {
          const newText = activeTextElement.text.slice(0, activeTextElement.cursorPosition - 1) +
                         activeTextElement.text.slice(activeTextElement.cursorPosition);
          setActiveTextElement({
            ...activeTextElement,
            text: newText,
            cursorPosition: activeTextElement.cursorPosition - 1
          });
        }
      } else if (e.key === 'Delete') {
        e.preventDefault();
        if (activeTextElement.cursorPosition < activeTextElement.text.length) {
          const newText = activeTextElement.text.slice(0, activeTextElement.cursorPosition) +
                         activeTextElement.text.slice(activeTextElement.cursorPosition + 1);
          setActiveTextElement({
            ...activeTextElement,
            text: newText
          });
        }
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault();
        if (activeTextElement.cursorPosition > 0) {
          setActiveTextElement({
            ...activeTextElement,
            cursorPosition: activeTextElement.cursorPosition - 1
          });
        }
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        if (activeTextElement.cursorPosition < activeTextElement.text.length) {
          setActiveTextElement({
            ...activeTextElement,
            cursorPosition: activeTextElement.cursorPosition + 1
          });
        }
      } else if (e.key === 'Home') {
        e.preventDefault();
        setActiveTextElement({
          ...activeTextElement,
          cursorPosition: 0
        });
      } else if (e.key === 'End') {
        e.preventDefault();
        setActiveTextElement({
          ...activeTextElement,
          cursorPosition: activeTextElement.text.length
        });
      } else if (e.key.length === 1 && !e.ctrlKey && !e.metaKey) {
        e.preventDefault();
        const newText = activeTextElement.text.slice(0, activeTextElement.cursorPosition) +
                       e.key +
                       activeTextElement.text.slice(activeTextElement.cursorPosition);
        setActiveTextElement({
          ...activeTextElement,
          text: newText,
          cursorPosition: activeTextElement.cursorPosition + 1
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTool, activeTextElement]);

  // Separate effect for general shortcuts (only when NOT in text editing mode)
  useEffect(() => {
    if (currentTool === 'text' && activeTextElement) {
      return; // Don't handle general shortcuts when editing text
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.activeElement?.tagName === 'INPUT') return;

      if (e.key === 'Delete' || e.key === 'Backspace') {
        // Delete selected elements
        if (selectionState.selectedElements.length > 0) {
          selectionState.selectedElements.forEach(elementId => {
            const elementToRemove = canvasState.elements.find(el => el.id === elementId);
            if (elementToRemove) {
              onDrawingAction(elementToRemove, 'remove');
            }
          });
          setSelectionState(prev => ({ ...prev, selectedElements: [] }));
          onSelectionChange?.([]);
          e.preventDefault();
        }
      } else if (e.key === 'Escape') {
        // Clear selection
        setSelectionState(prev => ({ ...prev, selectedElements: [] }));
        onSelectionChange?.([]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTool, activeTextElement, selectionState.selectedElements, canvasState.elements, onDrawingAction, onSelectionChange]);

  // Redraw canvas when text element changes
  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (canvas && ctx) {
      redrawCanvas(ctx);
    }
  }, [activeTextElement, redrawCanvas]);

  // Animation frame for cursor blinking - ONLY when text tool is active
  useEffect(() => {
    // Only animate when we have text tool active AND an active text element
    if (currentTool !== 'text' || !activeTextElement) {
      return;
    }

    let animationId: number;
    let isRunning = true;

    const animate = () => {
      // Triple check we should still be animating
      if (!isRunning || currentTool !== 'text' || !activeTextElement) {
        return;
      }

      const canvas = canvasRef.current;
      const ctx = canvas?.getContext('2d');
      if (canvas && ctx) {
        redrawCanvas(ctx);
      }

      if (isRunning) {
        animationId = requestAnimationFrame(animate);
      }
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      isRunning = false;
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [currentTool, activeTextElement, redrawCanvas]);

  // Handle wheel event for zooming
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const centerX = e.clientX - rect.left;
    const centerY = e.clientY - rect.top;

    const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1;
    onZoom(zoomDelta, centerX, centerY);
  };

  // Don't render on server side
  if (!isBrowser) {
    return (
      <div className="flex justify-center items-center h-96 border border-gray-300">
        <div className="text-gray-500">Loading canvas...</div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="relative w-full h-full bg-gray-100">
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        style={{
          cursor: currentTool === 'eraser' ? 'grab' :
                 currentTool === 'text' ? 'text' :
                 currentTool === 'pan' ? (isPanning ? 'grabbing' : 'grab') :
                 currentTool === 'select' ? 'default' : 'crosshair'
        }}
        title={currentTool === 'text' && !activeTextElement ? 'Click to add text' : ''}
      />

      {/* No text input overlay - typing happens directly on canvas */}
    </div>
  );
}
