'use client';

import React, { useState, useEffect } from 'react';
import { sessionManager, WhiteboardSession } from '@/lib/supabase-sessions';
import {
  <PERSON><PERSON>,
  Card,
  Heading,
  Text,
  Dialog,
  IconButton,
  Badge,
  Spinner
} from 'frosted-ui';
import { Plus, Trash2, Users, Calendar, User } from 'lucide-react';

// Type for user information from Whop (matching the actual SDK response)
interface WhopUser {
  id: string;
  name?: string | null;
  username: string;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

// Extended session type with creator info
interface SessionWithCreator extends WhiteboardSession {
  creatorInfo?: WhopUser;
  creatorLoading?: boolean;
}

interface SessionManagerProps {
  experienceId: string;
  userId: string;
  accessLevel: 'admin' | 'customer';
  onSessionSelect: (sessionId: string) => void;
  currentSessionId?: string;
}

export function SessionManager({
  experienceId,
  userId,
  accessLevel,
  onSessionSelect,
  currentSessionId
}: SessionManagerProps) {
  const [sessions, setSessions] = useState<SessionWithCreator[]>([]);
  const [loading, setLoading] = useState(true);
  const [newSessionName, setNewSessionName] = useState('');
  const [creating, setCreating] = useState(false);

  // Load sessions
  useEffect(() => {
    loadSessions();
  }, [experienceId]);

  const loadSessions = async () => {
    try {
      setLoading(true);
      const sessions = await sessionManager.getSessionsByExperience(experienceId);

      // Convert to SessionWithCreator and start loading creator info
      const sessionsWithCreator: SessionWithCreator[] = sessions.map(session => ({
        ...session,
        creatorLoading: true
      }));

      setSessions(sessionsWithCreator);

      // Load creator information for each session
      await loadCreatorInfo(sessionsWithCreator);
    } catch (error) {
      console.error('Error loading sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCreatorInfo = async (sessions: SessionWithCreator[]) => {
    // Load creator info for each session
    const updatedSessions = await Promise.all(
      sessions.map(async (session) => {
        try {
          const response = await fetch(`/api/users/${session.created_by}`);
          const result = await response.json();

          if (result.success && result.data) {
            return {
              ...session,
              creatorInfo: result.data,
              creatorLoading: false
            };
          } else {
            console.error(`Failed to load creator info for session ${session.id}:`, result.error);
            return {
              ...session,
              creatorLoading: false
            };
          }
        } catch (error) {
          console.error(`Error loading creator info for session ${session.id}:`, error);
          return {
            ...session,
            creatorLoading: false
          };
        }
      })
    );

    setSessions(updatedSessions);
  };

  const createSession = async () => {
    if (!newSessionName.trim()) return;

    try {
      setCreating(true);
      const session = await sessionManager.createSession(
        experienceId,
        newSessionName.trim(),
        userId
      );

      setNewSessionName('');
      await loadSessions();
      onSessionSelect(session.id);
    } catch (error) {
      console.error('Error creating session:', error);
      alert('Error creating session');
    } finally {
      setCreating(false);
    }
  };

  const deleteSession = async (sessionId: string) => {
    if (!confirm('Are you sure you want to delete this whiteboard session?')) {
      return;
    }

    try {
      await sessionManager.deleteSession(sessionId);
      await loadSessions();
      if (currentSessionId === sessionId) {
        // If current session was deleted, select another one or clear selection
        const remainingSessions = sessions.filter(s => s.id !== sessionId);
        if (remainingSessions.length > 0) {
          onSessionSelect(remainingSessions[0].id);
        }
      }
    } catch (error) {
      console.error('Error deleting session:', error);
      alert('Error deleting session');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-8">
          <Spinner size="3" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-8 pb-6">
        <div className="flex items-center justify-between">
          <div>
            <Heading size="7" className="mb-2 text-gray-900 font-bold">
              Your Whiteboards
            </Heading>
            <Text size="3" className="text-gray-600">
              Create or join a session to start collaborating
            </Text>
          </div>
          <Dialog.Root>
            <Dialog.Trigger>
              <Button
                size="3"
                className="bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 border border-blue-300"
                style={{ color: 'white !important' }}
              >
                <Plus size={18} color={"white"}/>
                <span style={{ color: 'white !important' }}>New Session</span>
              </Button>
            </Dialog.Trigger>
            <Dialog.Content className="max-w-md bg-white rounded-2xl shadow-2xl border-0">
              <Dialog.Title className="text-gray-900 text-xl font-bold mb-2">
                Create New Session
              </Dialog.Title>
              <Dialog.Description className="text-gray-600 mb-6">
                Give your whiteboard session a memorable name to get started.
              </Dialog.Description>
              <div className="space-y-6">
                <input
                  type="text"
                  placeholder="Enter session name..."
                  value={newSessionName}
                  onChange={(e) => setNewSessionName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && createSession()}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-gray-900 placeholder-gray-400 bg-gray-50 focus:bg-white"
                />
                <div className="flex justify-end space-x-3">
                  <Dialog.Close>
                    <Button variant="soft" color="gray" size="2" className="text-gray-600 hover:bg-gray-100">
                      Cancel
                    </Button>
                  </Dialog.Close>
                  <Button
                    onClick={createSession}
                    disabled={creating || !newSessionName.trim()}
                    size="2"
                    className="bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed border border-blue-300"
                    style={{ color: 'white !important' }}
                  >
                    <span style={{ color: 'white !important' }}>
                      {creating ? <Spinner size="1" /> : 'Create Session'}
                    </span>
                  </Button>
                </div>
              </div>
            </Dialog.Content>
          </Dialog.Root>
        </div>
      </div>



      {/* Sessions list */}
      <div className="flex-1 px-8 pb-8 overflow-y-auto">
        {sessions.length === 0 ? (
          <div className="h-full flex items-center justify-center pt-8">
            <div className="text-center max-w-md mx-auto">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-md border border-blue-200">
                <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <Heading size="6" className="mb-3 text-gray-900 font-bold">
                Ready to create magic?
              </Heading>
              <Text size="3" className="text-gray-600 mb-8 leading-relaxed">
                Start your first whiteboard session and bring your ideas to life.
              </Text>
              <Dialog.Root>
                <Dialog.Trigger>
                  
                </Dialog.Trigger>
                <Dialog.Content className="max-w-md bg-white rounded-2xl shadow-2xl border-0">
                  <Dialog.Title className="text-gray-900 text-xl font-bold mb-2">
                    Create New Session
                  </Dialog.Title>
                  <Dialog.Description className="text-gray-600 mb-6">
                    Give your whiteboard session a memorable name to get started.
                  </Dialog.Description>
                  <div className="space-y-6">
                    <input
                      type="text"
                      placeholder="Enter session name..."
                      value={newSessionName}
                      onChange={(e) => setNewSessionName(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && createSession()}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-gray-900 placeholder-gray-400 bg-gray-50 focus:bg-white"
                    />
                    <div className="flex justify-end space-x-3">
                      <Dialog.Close>
                        <Button variant="soft" color="gray" size="2" className="text-gray-600 hover:bg-gray-100">
                          Cancel
                        </Button>
                      </Dialog.Close>
                      <Button
                        onClick={createSession}
                        disabled={creating || !newSessionName.trim()}
                        size="2"
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        style={{ color: 'white !important' }}
                      >
                        <span style={{ color: 'white !important' }}>
                          {creating ? <Spinner size="1" /> : 'Create Session'}
                        </span>
                      </Button>
                    </div>
                  </div>
                </Dialog.Content>
              </Dialog.Root>
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {sessions.map(session => (
              <Card
                key={session.id}
                className={`group p-6 cursor-pointer transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 border-0 bg-white rounded-2xl shadow-lg ${
                  currentSessionId === session.id
                    ? 'ring-2 ring-blue-500 shadow-blue-200/50 bg-gradient-to-br from-blue-50 to-purple-50'
                    : 'hover:shadow-purple-200/30'
                }`}
                onClick={() => onSessionSelect(session.id)}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <Heading size="4" className="text-gray-900 font-bold line-clamp-1">
                        {session.name}
                      </Heading>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Calendar size={14} className="text-gray-400" />
                        <Text size="2" className="text-gray-600">
                          Updated {formatDate(session.updated_at)}
                        </Text>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users size={14} className="text-gray-400" />
                        <Badge variant="soft" size="1" className="bg-green-100 text-green-700 border-0">
                          Active
                        </Badge>
                      </div>

                      {/* Created by section */}
                      <div className="flex items-center space-x-2 pt-1">
                        <User size={14} className="text-gray-400" />
                        {session.creatorLoading ? (
                          <div className="flex items-center space-x-2">
                            <Spinner size="1" />
                            <Text size="2" className="text-gray-500">Loading creator...</Text>
                          </div>
                        ) : session.creatorInfo ? (
                          <div className="flex items-center space-x-2">
                            {session.creatorInfo.profilePicture?.sourceUrl ? (
                              <img
                                src={session.creatorInfo.profilePicture.sourceUrl}
                                alt={`${session.creatorInfo.name || session.creatorInfo.username}'s profile`}
                                className="w-5 h-5 rounded-full object-cover border border-gray-200"
                                onError={(e) => {
                                  // Fallback to default avatar if image fails to load
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  target.nextElementSibling?.classList.remove('hidden');
                                }}
                              />
                            ) : null}
                            <div className={`w-5 h-5 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center text-white text-xs font-medium ${session.creatorInfo.profilePicture?.sourceUrl ? 'hidden' : ''}`}>
                              {(session.creatorInfo.name || session.creatorInfo.username).charAt(0).toUpperCase()}
                            </div>
                            <Text size="2" className="text-gray-600">
                              Created by {session.creatorInfo.name || session.creatorInfo.username}
                            </Text>
                          </div>
                        ) : (
                          <Text size="2" className="text-gray-500">Created by unknown user</Text>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Delete button - only show for admins or session creators */}
                  {(accessLevel === 'admin' || session.created_by === userId) && (
                    <IconButton
                      variant="ghost"
                      size="1"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSession(session.id);
                      }}
                      className="opacity-0 group-hover:opacity-100 transition-all duration-200 text-red-500 hover:bg-red-50 hover:text-red-600"
                    >
                      <Trash2 size={14} />
                    </IconButton>
                  )}
                </div>

                {currentSessionId === session.id && (
                  <div className="flex items-center space-x-2 mt-4 p-3 bg-blue-100 rounded-xl">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <Text size="2" weight="medium" className="text-blue-700">
                      Currently Active
                    </Text>
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
