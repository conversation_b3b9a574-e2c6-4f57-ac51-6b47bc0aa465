'use client';

import React from 'react';
import {
  Pen,
  Eraser,
  Square,
  Circle,
  Minus,
  Type,
  MousePointer,
  Undo,
  Redo,
  Trash2,
  Hand,
  PaintBucket
} from 'lucide-react';
import { DrawingTool, ToolProperties, ViewportState, COLORS, FONT_SIZES } from '@/lib/types';
import {
  Icon<PERSON>utton,
  Popover,
  Text,
  Separator
} from 'frosted-ui';

interface ToolbarProps {
  currentTool: DrawingTool;
  toolProperties: ToolProperties;
  onToolChange: (tool: DrawingTool) => void;
  onPropertyChange: (properties: Partial<ToolProperties>) => void;
  onUndo: () => void;
  onRedo: () => void;
  onClear: () => void;
  onSave: () => void;
  canUndo: boolean;
  canRedo: boolean;
  viewport?: ViewportState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
}

export function Toolbar({
  currentTool,
  toolProperties,
  onToolChange,
  onPropertyChange,
  onUndo,
  onRedo,
  onClear,
  onSave,
  canUndo,
  canRedo
}: ToolbarProps) {
  const tools = [
    { tool: 'select' as DrawingTool, icon: MousePointer, label: 'Select' },
    { tool: 'pen' as DrawingTool, icon: Pen, label: 'Pen' },
    { tool: 'eraser' as DrawingTool, icon: Eraser, label: 'Eraser' },
    { tool: 'rectangle' as DrawingTool, icon: Square, label: 'Rectangle' },
    { tool: 'circle' as DrawingTool, icon: Circle, label: 'Circle' },
    { tool: 'line' as DrawingTool, icon: Minus, label: 'Line' },
    { tool: 'text' as DrawingTool, icon: Type, label: 'Text' },
    { tool: 'pan' as DrawingTool, icon: Hand, label: 'Pan' },
  ];

  const ToolButton = ({ tool, icon: Icon, label }: { tool: DrawingTool; icon: any; label: string }) => (
    <IconButton
      variant={currentTool === tool ? "solid" : "ghost"}
      color={currentTool === tool ? "blue" : "gray"}
      onClick={() => onToolChange(tool)}
      title={label}
      size="2"
    >
      <Icon size={16} />
    </IconButton>
  );

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 px-4 py-3">
      <div className="flex items-center space-x-2">
        {/* Drawing Tools */}
        {tools.map(toolItem => (
          <ToolButton key={toolItem.tool} {...toolItem} />
        ))}

        <Separator orientation="vertical" className="mx-2 h-6 bg-gray-300" />

        {/* Color Picker */}
        <Popover.Root>
          <Popover.Trigger>
            <IconButton variant="ghost" title="Color" size="2" className="hover:bg-gray-100">
              <div
                className="w-4 h-4 rounded border-2 border-gray-400"
                style={{ backgroundColor: toolProperties.color }}
              />
            </IconButton>
          </Popover.Trigger>
          <Popover.Content className="w-56 bg-white border border-gray-200 shadow-lg p-4">
            <Text size="2" weight="medium" className="mb-3 text-gray-900" style={{ color: '#1a1a1a' }}>
              Stroke Color
            </Text>
            <div className="grid grid-cols-6 gap-2 mb-4">
              {COLORS.map(color => (
                <button
                  key={color}
                  onClick={() => onPropertyChange({ color })}
                  className={`w-6 h-6 rounded border-2 hover:scale-110 transition-transform ${
                    toolProperties.color === color ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-400'
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>

            {/* Fill options for shapes */}
            {['rectangle', 'circle'].includes(currentTool) && (
              <>
                <Separator className="my-3" />
                <div className="space-y-3">
                  <Text size="2" weight="medium" className="text-gray-900" style={{ color: '#1a1a1a' }}>
                    Fill
                  </Text>
                  <div className="flex items-center space-x-2">
                    <IconButton
                      variant={!toolProperties.filled ? "solid" : "ghost"}
                      color={!toolProperties.filled ? "blue" : "gray"}
                      onClick={() => onPropertyChange({ filled: false })}
                      title="No Fill"
                      size="1"
                    >
                      <Square size={14} />
                    </IconButton>
                    <IconButton
                      variant={toolProperties.filled ? "solid" : "ghost"}
                      color={toolProperties.filled ? "blue" : "gray"}
                      onClick={() => onPropertyChange({ filled: true })}
                      title="Fill"
                      size="1"
                    >
                      <PaintBucket size={14} />
                    </IconButton>
                  </div>

                  {toolProperties.filled && (
                    <>
                      <Text size="2" weight="medium" className="text-gray-900" style={{ color: '#1a1a1a' }}>
                        Fill Color
                      </Text>
                      <div className="grid grid-cols-6 gap-2">
                        {COLORS.map(color => (
                          <button
                            key={`fill-${color}`}
                            onClick={() => onPropertyChange({ fillColor: color })}
                            className={`w-6 h-6 rounded border-2 hover:scale-110 transition-transform ${
                              toolProperties.fillColor === color ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-400'
                            }`}
                            style={{ backgroundColor: color }}
                            title={`Fill: ${color}`}
                          />
                        ))}
                      </div>
                    </>
                  )}
                </div>
              </>
            )}
          </Popover.Content>
        </Popover.Root>

        {/* Font Size for Text Tool */}
        {currentTool === 'text' && (
          <Popover.Root>
            <Popover.Trigger>
              <IconButton variant="ghost" title="Font Size" size="2" className="hover:bg-gray-100">
                <div className="text-sm font-medium min-w-[32px]">
                  {toolProperties.fontSize}
                </div>
              </IconButton>
            </Popover.Trigger>
            <Popover.Content className="w-40 bg-white border border-gray-200 shadow-lg p-2 rounded-lg">
              <div className="grid grid-cols-3 gap-1">
                {FONT_SIZES.map(size => (
                  <button
                    key={size}
                    onClick={() => onPropertyChange({ fontSize: size })}
                    className={`px-2 py-1 text-sm rounded hover:bg-gray-100 transition-colors ${
                      toolProperties.fontSize === size ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-700'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </Popover.Content>
          </Popover.Root>
        )}

        <Separator orientation="vertical" className="mx-2 h-6 bg-gray-300" />

        {/* Action Buttons */}
        <IconButton
          variant="ghost"
          onClick={onUndo}
          disabled={!canUndo}
          title="Undo"
          size="2"
        >
          <Undo size={16} />
        </IconButton>

        <IconButton
          variant="ghost"
          onClick={onRedo}
          disabled={!canRedo}
          title="Redo"
          size="2"
        >
          <Redo size={16} />
        </IconButton>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <IconButton
          variant="ghost"
          color="red"
          onClick={onClear}
          title="Clear Canvas"
          size="2"
        >
          <Trash2 size={16} />
        </IconButton>
      </div>
    </div>
  );
}
