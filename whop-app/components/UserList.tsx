'use client';

import React from 'react';
import { UserCursor } from '@/lib/types';
import { Users } from 'lucide-react';
import { Avatar, AvatarGroup, Text, Badge } from 'frosted-ui';

interface UserListProps {
  users: UserCursor[];
  currentUserId: string;
}

export function UserList({ users, currentUserId }: UserListProps) {
  const activeUsers = users.filter(user =>
    Date.now() - user.lastSeen < 30000 // Active within last 30 seconds
  );

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2 bg-gray-100 px-3 py-1.5 rounded-full border border-gray-200">
        <Users size={14} className="text-gray-600" />
        <Text size="2" weight="medium" className="text-gray-700" style={{ color: '#374151' }}>
          {activeUsers.length + 1} online
        </Text>
      </div>

      <AvatarGroup.Root>
        {/* Current user indicator */}
        <AvatarGroup.Avatar
          fallback="You"
          color="blue"
          sizes="2"
        />

        {/* Other active users */}
        {activeUsers.slice(0, 3).map(user => (
          <AvatarGroup.Avatar
            key={user.userId}
            fallback={user.username.charAt(0).toUpperCase()}
            style={{ backgroundColor: user.color }}
            sizes="2"
          />
        ))}

        {/* Show count if more than 3 users (since we show current user + 3 others) */}
        {activeUsers.length > 3 && (
          <AvatarGroup.Avatar
            fallback={`+${activeUsers.length - 3}`}
            color="gray"
            sizes="2"
          />
        )}
      </AvatarGroup.Root>
    </div>
  );
}
