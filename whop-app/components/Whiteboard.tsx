'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { SupabaseRealtimeClient } from '@/lib/supabase-realtime';
import { sessionManager } from '@/lib/supabase-sessions';
import { WhiteboardProps, DrawingTool, ToolProperties, DrawingElement, UserCursor, CanvasState, ViewportState, DEFAULT_TOOL_PROPERTIES, DEFAULT_VIEWPORT, INFINITE_CANVAS_SIZE } from '@/lib/types';
import { Toolbar } from './Toolbar';
import { Canvas } from './Canvas';
import { IconButton } from 'frosted-ui';

// Check if we're in the browser
const isBrowser = typeof window !== 'undefined';

export function Whiteboard({ sessionId, userId, username, experienceId, accessLevel }: WhiteboardProps) {
  // State management
  const [currentTool, setCurrentTool] = useState<DrawingTool>('pen');
  const [toolProperties, setToolProperties] = useState<ToolProperties>(DEFAULT_TOOL_PROPERTIES);
  const [canvasState, setCanvasState] = useState<CanvasState>({
    elements: [],
    background: '#ffffff',
    width: INFINITE_CANVAS_SIZE,
    height: INFINITE_CANVAS_SIZE,
    viewport: DEFAULT_VIEWPORT
  });
  const [activeUsers, setActiveUsers] = useState<UserCursor[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [history, setHistory] = useState<CanvasState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Refs
  const realtimeClientRef = useRef<SupabaseRealtimeClient | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Load session data from Supabase
  useEffect(() => {
    if (!sessionId) return;

    const loadSessionData = async () => {
      try {
        const session = await sessionManager.getSession(sessionId);
        if (session && session.data) {
          // Ensure viewport exists in loaded data, merge with default if missing
          const loadedData = {
            ...session.data,
            viewport: session.data.viewport || DEFAULT_VIEWPORT
          };
          setCanvasState(loadedData);
        }
      } catch (error) {
        console.error('Error loading session data:', error);
      }
    };

    loadSessionData();
  }, [sessionId]);

  // Save canvas state to Supabase when it changes
  useEffect(() => {
    if (!sessionId || !canvasState.elements.length) return;

    const saveTimeout = setTimeout(async () => {
      try {
        await sessionManager.updateSession(sessionId, canvasState);
      } catch (error) {
        console.error('Error saving canvas state:', error);
      }
    }, 1000); // Debounce saves by 1 second

    return () => clearTimeout(saveTimeout);
  }, [sessionId, canvasState]);

  // Initialize Supabase Realtime connection
  useEffect(() => {
    if (!isBrowser || !sessionId) return;

    console.log('Initializing Supabase Realtime connection...');
    const realtimeClient = new SupabaseRealtimeClient();
    realtimeClientRef.current = realtimeClient;

    // Set up event handlers
    realtimeClient.onDrawingAction((data) => {
      if (data.userId !== userId) {
        handleDrawingAction(data.element, data.action);
      }
    });

    realtimeClient.onCursorMove((data) => {
      if (data.userId !== userId) {
        setActiveUsers(prev => {
          const updated = prev.filter(user => user.userId !== data.userId);
          updated.push({
            userId: data.userId,
            username: data.username,
            x: data.x,
            y: data.y,
            color: getUserColor(data.userId),
            lastSeen: Date.now()
          });
          return updated;
        });
      }
    });

    realtimeClient.onUserJoined((data) => {
      console.log(`${data.username} joined the session`);
    });

    realtimeClient.onUserLeft((data) => {
      console.log(`${data.username} left the session`);
      setActiveUsers(prev => prev.filter(user => user.userId !== data.userId));
    });

    realtimeClient.onClearCanvas(() => {
      setCanvasState({
        elements: [],
        background: '#ffffff',
        width: INFINITE_CANVAS_SIZE,
        height: INFINITE_CANVAS_SIZE,
        viewport: DEFAULT_VIEWPORT
      });
    });

    // Join the session
    realtimeClient.joinSession(sessionId, userId, username)
      .then(() => {
        console.log('Successfully joined session:', sessionId);
        setIsConnected(true);
      })
      .catch((error) => {
        console.error('Failed to join session:', error);
        setIsConnected(false);
      });



    return () => {
      realtimeClient.leaveSession();
    };
  }, [sessionId, userId, username]);

  // Generate consistent color for user
  const getUserColor = (userId: string): string => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8', '#f7dc6f'];
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  // Handle drawing actions
  const handleDrawingAction = useCallback((element: DrawingElement, action: 'add' | 'update' | 'remove') => {
    // Update local state and add to history
    setCanvasState(prev => {
      const newState = { ...prev };

      if (action === 'add') {
        newState.elements = [...prev.elements, element];
      } else if (action === 'update') {
        const index = prev.elements.findIndex(el => el.id === element.id);
        if (index !== -1) {
          newState.elements = [...prev.elements];
          newState.elements[index] = element;
        }
      } else if (action === 'remove') {
        newState.elements = prev.elements.filter(el => el.id !== element.id);
      }

      // Add to history for undo/redo (for all actions to enable unlimited undo/redo)
      setTimeout(() => {
        setHistory(currentHistory => {
          const newHistory = currentHistory.slice(0, historyIndex + 1);
          newHistory.push(newState);
          setHistoryIndex(newHistory.length - 1);
          // Keep unlimited history - no artificial limits
          return newHistory;
        });
      }, 0);

      return newState;
    });

    // Emit to other users
    if (realtimeClientRef.current && isConnected) {
      realtimeClientRef.current.sendDrawingAction(element, action);
    }
  }, [historyIndex, isConnected]);

  // Handle cursor movement
  const handleCursorMove = useCallback((x: number, y: number) => {
    if (realtimeClientRef.current && isConnected) {
      realtimeClientRef.current.sendCursorMove(x, y);
    }
  }, [isConnected]);

  // Handle undo
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      const historyState = history[newIndex];
      // Ensure the history state has a viewport
      setCanvasState({
        ...historyState,
        viewport: historyState.viewport || DEFAULT_VIEWPORT
      });
    }
  }, [history, historyIndex]);

  // Handle redo
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      const historyState = history[newIndex];
      // Ensure the history state has a viewport
      setCanvasState({
        ...historyState,
        viewport: historyState.viewport || DEFAULT_VIEWPORT
      });
    }
  }, [history, historyIndex]);

  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'z' && !e.shiftKey) {
          e.preventDefault();
          handleUndo();
        } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
          e.preventDefault();
          handleRedo();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleUndo, handleRedo]);

  // Handle viewport changes
  const handleViewportChange = useCallback((viewport: ViewportState) => {
    setCanvasState(prev => ({
      ...prev,
      viewport
    }));
  }, []);

  // Handle zoom
  const handleZoom = useCallback((delta: number, centerX?: number, centerY?: number) => {
    setCanvasState(prev => {
      const newZoom = Math.max(0.1, Math.min(5.0, prev.viewport.zoom + delta));

      // If center point is provided, zoom towards that point
      if (centerX !== undefined && centerY !== undefined) {
        const zoomRatio = newZoom / prev.viewport.zoom;
        const newPanX = centerX - (centerX - prev.viewport.panX) * zoomRatio;
        const newPanY = centerY - (centerY - prev.viewport.panY) * zoomRatio;

        return {
          ...prev,
          viewport: {
            ...prev.viewport,
            zoom: newZoom,
            panX: newPanX,
            panY: newPanY
          }
        };
      }

      return {
        ...prev,
        viewport: {
          ...prev.viewport,
          zoom: newZoom
        }
      };
    });
  }, []);

  // Handle pan
  const handlePan = useCallback((deltaX: number, deltaY: number) => {
    setCanvasState(prev => ({
      ...prev,
      viewport: {
        ...prev.viewport,
        panX: prev.viewport.panX + deltaX,
        panY: prev.viewport.panY + deltaY
      }
    }));
  }, []);

  // Reset viewport
  const handleResetViewport = useCallback(() => {
    setCanvasState(prev => ({
      ...prev,
      viewport: DEFAULT_VIEWPORT
    }));
  }, []);

  // Handle clear canvas
  const handleClear = useCallback(() => {
    if (realtimeClientRef.current && isConnected) {
      realtimeClientRef.current.sendClearCanvas();
    }

    const newState: CanvasState = {
      elements: [],
      background: '#ffffff',
      width: INFINITE_CANVAS_SIZE,
      height: INFINITE_CANVAS_SIZE,
      viewport: DEFAULT_VIEWPORT
    };

    setCanvasState(newState);
    setHistory([newState]);
    setHistoryIndex(0);
  }, [isConnected]);

  // Handle save
  const handleSave = useCallback(async () => {
    try {
      await sessionManager.updateSession(sessionId, canvasState);
      console.log('Canvas saved successfully');
    } catch (error) {
      console.error('Error saving canvas:', error);
    }
  }, [sessionId, canvasState]);

  // Don't render on server side
  if (!isBrowser) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gray-100 overflow-hidden relative">
      {/* Canvas area - takes full space */}
      <div className="flex-1 overflow-hidden">
        <Canvas
          sessionId={sessionId}
          userId={userId}
          username={username}
          currentTool={currentTool}
          toolProperties={toolProperties}
          canvasState={canvasState}
          activeUsers={activeUsers}
          onDrawingAction={handleDrawingAction}
          onCursorMove={handleCursorMove}
          onZoom={handleZoom}
          onPan={handlePan}
          onSelectionChange={(selectedElements: string[]) => {
            // Handle selection changes if needed
            console.log('Selected elements:', selectedElements);
          }}
        />
      </div>

      {/* Bottom Toolbar */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10">
        <Toolbar
          currentTool={currentTool}
          toolProperties={toolProperties}
          onToolChange={(tool) => {
            // Immediately set the new tool - this will trigger the Canvas useEffect to clear text elements
            setCurrentTool(tool);
          }}
          onPropertyChange={(props) => setToolProperties(prev => ({ ...prev, ...props }))}
          onUndo={handleUndo}
          onRedo={handleRedo}
          onClear={handleClear}
          onSave={handleSave}
          canUndo={historyIndex > 0}
          canRedo={historyIndex < history.length - 1}
          viewport={canvasState.viewport}
          onZoomIn={() => handleZoom(0.1)}
          onZoomOut={() => handleZoom(-0.1)}
          onResetZoom={handleResetViewport}
        />
      </div>

      {/* Floating zoom controls - bottom right */}
      <div className="absolute bottom-6 right-6 flex flex-col space-y-2 z-10">
        <IconButton
          variant="solid"
          color="gray"
          onClick={() => handleZoom(0.1)}
          size="2"
          className="bg-white shadow-lg border border-gray-200 hover:bg-gray-50"
        >
          +
        </IconButton>
        <IconButton
          variant="solid"
          color="gray"
          onClick={() => handleZoom(-0.1)}
          size="2"
          className="bg-white shadow-lg border border-gray-200 hover:bg-gray-50"
        >
          -
        </IconButton>
        <IconButton
          variant="solid"
          color="gray"
          onClick={handleResetViewport}
          size="2"
          className="bg-white shadow-lg border border-gray-200 hover:bg-gray-50"
          title="Reset Zoom"
        >
          ?
        </IconButton>
      </div>
    </div>
  );
}
