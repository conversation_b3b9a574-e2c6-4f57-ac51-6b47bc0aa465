'use client';

import React, { useState, useEffect } from 'react';
import { Whiteboard } from './Whiteboard';
import { SessionManager } from './SessionManager';
import { sessionManager, WhiteboardSession } from '@/lib/supabase-sessions';
import {
  Card,
  Heading,
  Text,
  Button,
  Spinner,
  IconButton
} from 'frosted-ui';
import { ArrowLeft } from 'lucide-react';

interface WhiteboardAppProps {
  experienceId: string;
  userId: string;
  username: string;
  accessLevel: 'admin' | 'customer';
}

export function WhiteboardApp({ experienceId, userId, username, accessLevel }: WhiteboardAppProps) {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentSession, setCurrentSession] = useState<WhiteboardSession | null>(null);
  const [showSessionManager, setShowSessionManager] = useState(true);
  const [loading, setLoading] = useState(true);

  // Load initial session or show session manager
  useEffect(() => {
    loadInitialSession();
  }, [experienceId]);

  const loadInitialSession = async () => {
    try {
      // Try to load existing sessions
      const sessions = await sessionManager.getSessionsByExperience(experienceId);

      if (sessions.length > 0) {
        // If there are existing sessions, show the session manager
        setShowSessionManager(true);
      } else {
        // If no sessions exist, show the session manager to create one
        setShowSessionManager(true);
      }
    } catch (error) {
      console.error('Error loading initial session:', error);
      setShowSessionManager(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSessionSelect = async (sessionId: string) => {
    try {
      // Load the session details to get the name
      const session = await sessionManager.getSession(sessionId);
      setCurrentSession(session);
      setCurrentSessionId(sessionId);
      setShowSessionManager(false);
    } catch (error) {
      console.error('Error loading session details:', error);
      // Fallback: still set the session ID even if we can't load details
      setCurrentSessionId(sessionId);
      setShowSessionManager(false);
    }
  };

  const handleBackToSessions = () => {
    setShowSessionManager(true);
    setCurrentSessionId(null);
    setCurrentSession(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner size="3" />
      </div>
    );
  }

  if (showSessionManager || !currentSessionId) {
    return (
      <div className="h-screen overflow-hidden bg-white">
        <SessionManager
          experienceId={experienceId}
          userId={userId}
          accessLevel={accessLevel}
          onSessionSelect={handleSessionSelect}
          currentSessionId={currentSessionId || undefined}
        />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100 overflow-hidden">
      {/* Top Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-3 flex items-center">
        {/* Left side - Title only */}
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            onClick={handleBackToSessions}
            size="1"
            className="hover:bg-gray-100 text-gray-600 p-1"
          >
            <ArrowLeft size={14} />
          </Button>
          <Text size="3" weight="medium" className="text-gray-900">
            {currentSession?.name || 'Untitled'}
          </Text>
        </div>
      </div>

      {/* Whiteboard */}
      <div className="flex-1 overflow-hidden">
        <Whiteboard
          sessionId={currentSessionId}
          userId={userId}
          username={username}
          experienceId={experienceId}
          accessLevel={accessLevel}
        />
      </div>
    </div>
  );
}
