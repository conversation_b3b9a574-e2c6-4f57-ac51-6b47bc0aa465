'use client';

import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import the WhiteboardApp to avoid SSR issues
const WhiteboardApp = dynamic(() => import('./WhiteboardApp').then(mod => ({ default: mod.WhiteboardApp })), {
  ssr: false,
  loading: () => (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Collaborative Whiteboard...</p>
      </div>
    </div>
  )
});

interface WhiteboardWrapperProps {
  experienceId: string;
  userId: string;
  username: string;
  accessLevel: 'admin' | 'customer';
}

export function WhiteboardWrapper(props: WhiteboardWrapperProps) {
  return <WhiteboardApp {...props} />;
}
