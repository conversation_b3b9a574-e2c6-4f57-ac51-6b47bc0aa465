import { supabase } from './supabase';
import { DrawingElement, UserCursor } from './types';
import type { RealtimeChannel } from '@supabase/supabase-js';

export interface DrawingActionPayload {
  element: DrawingElement;
  action: 'add' | 'update' | 'remove';
  userId: string;
  timestamp: number;
}

export interface CursorMovePayload {
  userId: string;
  username: string;
  x: number;
  y: number;
  timestamp: number;
}

export interface UserJoinedPayload {
  userId: string;
  username: string;
  timestamp: number;
}

export interface UserLeftPayload {
  userId: string;
  username: string;
  timestamp: number;
}

export interface ClearCanvasPayload {
  userId: string;
  timestamp: number;
}

export class SupabaseRealtimeClient {
  private channel: RealtimeChannel | null = null;
  private sessionId: string | null = null;
  private userId: string | null = null;
  private username: string | null = null;

  // Event handlers
  private onDrawingActionHandler?: (data: DrawingActionPayload) => void;
  private onCursorMoveHandler?: (data: CursorMovePayload) => void;
  private onUserJoinedHandler?: (data: UserJoinedPayload) => void;
  private onUserLeftHandler?: (data: UserLeftPayload) => void;
  private onClearCanvasHandler?: (data: ClearCanvasPayload) => void;
  private onCanvasStateHandler?: (data: any) => void;

  constructor() {
    // Initialize Supabase real-time connection
  }

  // Join a whiteboard session
  async joinSession(sessionId: string, userId: string, username: string): Promise<void> {
    if (this.channel) {
      await this.leaveSession();
    }

    this.sessionId = sessionId;
    this.userId = userId;
    this.username = username;

    // Create a channel for this session
    this.channel = supabase.channel(`whiteboard:${sessionId}`, {
      config: {
        broadcast: { self: false },
        presence: { key: userId },
      },
    });

    // Set up event listeners
    this.setupEventListeners();

    // Subscribe to the channel
    this.channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        console.log('Successfully joined session:', sessionId);

        // Track user presence
        await this.channel?.track({
          userId,
          username,
          online_at: new Date().toISOString(),
        });

        // Notify others that user joined
        this.channel?.send({
          type: 'broadcast',
          event: 'user_joined',
          payload: {
            userId,
            username,
            timestamp: Date.now(),
          },
        });
      } else if (status === 'CHANNEL_ERROR') {
        throw new Error('Failed to join session');
      }
    });
  }

  // Leave the current session
  async leaveSession(): Promise<void> {
    if (this.channel && this.userId && this.username) {
      // Notify others that user left
      this.channel.send({
        type: 'broadcast',
        event: 'user_left',
        payload: {
          userId: this.userId,
          username: this.username,
          timestamp: Date.now(),
        },
      });

      // Unsubscribe from the channel
      await this.channel.unsubscribe();
      this.channel = null;
    }

    this.sessionId = null;
    this.userId = null;
    this.username = null;
  }

  // Send drawing action
  sendDrawingAction(element: DrawingElement, action: 'add' | 'update' | 'remove'): void {
    if (!this.channel || !this.userId) return;

    this.channel.send({
      type: 'broadcast',
      event: 'drawing_action',
      payload: {
        element,
        action,
        userId: this.userId,
        timestamp: Date.now(),
      },
    });
  }

  // Send cursor movement
  sendCursorMove(x: number, y: number): void {
    if (!this.channel || !this.userId || !this.username) return;

    this.channel.send({
      type: 'broadcast',
      event: 'cursor_move',
      payload: {
        userId: this.userId,
        username: this.username,
        x,
        y,
        timestamp: Date.now(),
      },
    });
  }

  // Send clear canvas
  sendClearCanvas(): void {
    if (!this.channel || !this.userId) return;

    this.channel.send({
      type: 'broadcast',
      event: 'clear_canvas',
      payload: {
        userId: this.userId,
        timestamp: Date.now(),
      },
    });
  }

  // Event handler setters
  onDrawingAction(handler: (data: DrawingActionPayload) => void): void {
    this.onDrawingActionHandler = handler;
  }

  onCursorMove(handler: (data: CursorMovePayload) => void): void {
    this.onCursorMoveHandler = handler;
  }

  onUserJoined(handler: (data: UserJoinedPayload) => void): void {
    this.onUserJoinedHandler = handler;
  }

  onUserLeft(handler: (data: UserLeftPayload) => void): void {
    this.onUserLeftHandler = handler;
  }

  onClearCanvas(handler: (data: ClearCanvasPayload) => void): void {
    this.onClearCanvasHandler = handler;
  }

  onCanvasState(handler: (data: any) => void): void {
    this.onCanvasStateHandler = handler;
  }

  // Get active users from presence
  getActiveUsers(): UserCursor[] {
    if (!this.channel) return [];

    const presenceState = this.channel.presenceState();
    const users: UserCursor[] = [];

    Object.values(presenceState).forEach((presences: any) => {
      presences.forEach((presence: any) => {
        if (presence.userId !== this.userId) {
          users.push({
            userId: presence.userId,
            username: presence.username,
            x: presence.cursor_x || 0,
            y: presence.cursor_y || 0,
            color: `hsl(${Math.abs(presence.userId.hashCode()) % 360}, 70%, 50%)`,
            lastSeen: Date.now(),
          });
        }
      });
    });

    return users;
  }

  // Private method to set up event listeners
  private setupEventListeners(): void {
    if (!this.channel) return;

    // Listen for drawing actions
    this.channel.on('broadcast', { event: 'drawing_action' }, (payload) => {
      if (this.onDrawingActionHandler) {
        this.onDrawingActionHandler(payload.payload as DrawingActionPayload);
      }
    });

    // Listen for cursor movements
    this.channel.on('broadcast', { event: 'cursor_move' }, (payload) => {
      if (this.onCursorMoveHandler) {
        this.onCursorMoveHandler(payload.payload as CursorMovePayload);
      }
    });

    // Listen for user joined events
    this.channel.on('broadcast', { event: 'user_joined' }, (payload) => {
      if (this.onUserJoinedHandler) {
        this.onUserJoinedHandler(payload.payload as UserJoinedPayload);
      }
    });

    // Listen for user left events
    this.channel.on('broadcast', { event: 'user_left' }, (payload) => {
      if (this.onUserLeftHandler) {
        this.onUserLeftHandler(payload.payload as UserLeftPayload);
      }
    });

    // Listen for clear canvas events
    this.channel.on('broadcast', { event: 'clear_canvas' }, (payload) => {
      if (this.onClearCanvasHandler) {
        this.onClearCanvasHandler(payload.payload as ClearCanvasPayload);
      }
    });

    // Listen for presence changes
    this.channel.on('presence', { event: 'sync' }, () => {
      // Presence state has changed, you can update active users list
      console.log('Presence synced');
    });

    this.channel.on('presence', { event: 'join' }, ({ key, newPresences }) => {
      console.log('User joined presence:', key, newPresences);
    });

    this.channel.on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
      console.log('User left presence:', key, leftPresences);
    });
  }
}

// Utility function to generate hash code for consistent colors
declare global {
  interface String {
    hashCode(): number;
  }
}

String.prototype.hashCode = function() {
  let hash = 0;
  if (this.length === 0) return hash;
  for (let i = 0; i < this.length; i++) {
    const char = this.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
};
