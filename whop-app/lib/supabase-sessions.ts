import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

export interface WhiteboardSession {
  id: string;
  experience_id: string;
  name: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  data: any; // Canvas data
}

export interface DrawingAction {
  id: string;
  session_id: string;
  user_id: string;
  username: string;
  action_type: 'add' | 'update' | 'remove' | 'clear';
  element_data: any;
  created_at: string;
}

export class SupabaseSessionManager {
  // Get all sessions for an experience
  async getSessionsByExperience(experienceId: string): Promise<WhiteboardSession[]> {
    const { data, error } = await supabase
      .from('whiteboard_sessions')
      .select('*')
      .eq('experience_id', experienceId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching sessions:', error);
      throw error;
    }

    return data || [];
  }

  // Get a specific session
  async getSession(sessionId: string): Promise<WhiteboardSession | null> {
    const { data, error } = await supabase
      .from('whiteboard_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Session not found
      }
      console.error('Error fetching session:', error);
      throw error;
    }

    return data;
  }

  // Create a new session
  async createSession(
    experienceId: string,
    name: string,
    createdBy: string
  ): Promise<WhiteboardSession> {
    const sessionId = uuidv4();
    const initialData = {
      elements: [],
      background: '#ffffff',
      width: 10000, // Use INFINITE_CANVAS_SIZE
      height: 10000, // Use INFINITE_CANVAS_SIZE
      viewport: {
        zoom: 1.0,
        panX: 0,
        panY: 0
      }
    };

    const { data, error } = await supabase
      .from('whiteboard_sessions')
      .insert({
        id: sessionId,
        experience_id: experienceId,
        name,
        created_by: createdBy,
        data: initialData
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating session:', error);
      throw error;
    }

    return data;
  }

  // Update session data
  async updateSession(sessionId: string, data: any): Promise<void> {
    const { error } = await supabase
      .from('whiteboard_sessions')
      .update({
        data,
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionId);

    if (error) {
      console.error('Error updating session:', error);
      throw error;
    }
  }

  // Delete a session
  async deleteSession(sessionId: string): Promise<void> {
    const { error } = await supabase
      .from('whiteboard_sessions')
      .delete()
      .eq('id', sessionId);

    if (error) {
      console.error('Error deleting session:', error);
      throw error;
    }
  }

  // Add a drawing action
  async addDrawingAction(
    sessionId: string,
    userId: string,
    username: string,
    actionType: 'add' | 'update' | 'remove' | 'clear',
    elementData: any
  ): Promise<void> {
    const { error } = await supabase
      .from('drawing_actions')
      .insert({
        id: uuidv4(),
        session_id: sessionId,
        user_id: userId,
        username,
        action_type: actionType,
        element_data: elementData,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error adding drawing action:', error);
      throw error;
    }
  }

  // Get drawing actions for a session
  async getDrawingActions(sessionId: string): Promise<DrawingAction[]> {
    const { data, error } = await supabase
      .from('drawing_actions')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching drawing actions:', error);
      throw error;
    }

    return data || [];
  }
}

// Singleton instance
export const sessionManager = new SupabaseSessionManager();
