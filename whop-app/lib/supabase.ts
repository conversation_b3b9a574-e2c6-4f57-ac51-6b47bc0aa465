import { createClient } from '@supabase/supabase-js';

// Supabase configuration for frontend-only setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!;

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
}
if (!supabaseAnonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY or NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY is required');
}

// Single Supabase client for frontend use
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // We'll handle auth through Whop
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Database types (generated from Supabase)
export interface Database {
  public: {
    Tables: {
      whiteboard_sessions: {
        Row: {
          id: string;
          experience_id: string;
          name: string;
          created_by: string;
          created_at: string;
          updated_at: string;
          data: any; // JSONB
        };
        Insert: {
          id?: string;
          experience_id: string;
          name: string;
          created_by: string;
          created_at?: string;
          updated_at?: string;
          data?: any;
        };
        Update: {
          id?: string;
          experience_id?: string;
          name?: string;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          data?: any;
        };
      };
      drawing_actions: {
        Row: {
          id: string;
          session_id: string;
          user_id: string;
          action: any; // JSONB
          timestamp: string;
          action_type: 'draw' | 'erase' | 'shape' | 'text' | 'clear';
        };
        Insert: {
          id?: string;
          session_id: string;
          user_id: string;
          action: any;
          timestamp?: string;
          action_type: 'draw' | 'erase' | 'shape' | 'text' | 'clear';
        };
        Update: {
          id?: string;
          session_id?: string;
          user_id?: string;
          action?: any;
          timestamp?: string;
          action_type?: 'draw' | 'erase' | 'shape' | 'text' | 'clear';
        };
      };
      user_sessions: {
        Row: {
          user_id: string;
          session_id: string;
          username: string;
          joined_at: string;
          last_seen: string;
          cursor_x: number | null;
          cursor_y: number | null;
        };
        Insert: {
          user_id: string;
          session_id: string;
          username: string;
          joined_at?: string;
          last_seen?: string;
          cursor_x?: number | null;
          cursor_y?: number | null;
        };
        Update: {
          user_id?: string;
          session_id?: string;
          username?: string;
          joined_at?: string;
          last_seen?: string;
          cursor_x?: number | null;
          cursor_y?: number | null;
        };
      };
    };
  };
}

// Type helpers
export type WhiteboardSession = Database['public']['Tables']['whiteboard_sessions']['Row'];
export type DrawingAction = Database['public']['Tables']['drawing_actions']['Row'];
export type UserSession = Database['public']['Tables']['user_sessions']['Row'];

export type WhiteboardSessionInsert = Database['public']['Tables']['whiteboard_sessions']['Insert'];
export type DrawingActionInsert = Database['public']['Tables']['drawing_actions']['Insert'];
export type UserSessionInsert = Database['public']['Tables']['user_sessions']['Insert'];

export type WhiteboardSessionUpdate = Database['public']['Tables']['whiteboard_sessions']['Update'];
export type DrawingActionUpdate = Database['public']['Tables']['drawing_actions']['Update'];
export type UserSessionUpdate = Database['public']['Tables']['user_sessions']['Update'];
