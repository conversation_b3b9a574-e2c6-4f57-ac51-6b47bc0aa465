// Drawing tool types
export type DrawingTool = 'pen' | 'eraser' | 'rectangle' | 'circle' | 'line' | 'text' | 'select' | 'pan';

// Drawing action types
export interface Point {
  x: number;
  y: number;
}

export interface DrawingPath {
  id: string;
  tool: DrawingTool;
  points: Point[];
  color: string;
  strokeWidth: number;
  opacity: number;
  timestamp: number;
  userId: string;
}

export interface Shape {
  id: string;
  type: 'rectangle' | 'circle' | 'line';
  startPoint: Point;
  endPoint: Point;
  color: string;
  strokeWidth: number;
  opacity: number;
  filled: boolean;
  fillColor?: string;
  timestamp: number;
  userId: string;
}

export interface TextElement {
  id: string;
  position: Point;
  text: string;
  fontSize: number;
  color: string;
  fontFamily: string;
  opacity: number;
  timestamp: number;
  userId: string;
}

export type DrawingElement = DrawingPath | Shape | TextElement;

// Selection state
export interface SelectionState {
  selectedElements: string[]; // Array of element IDs
  selectionBounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isDragging: boolean;
  dragOffset?: Point;
}

// Viewport state for infinite canvas
export interface ViewportState {
  zoom: number;
  panX: number;
  panY: number;
}

// Canvas state
export interface CanvasState {
  elements: DrawingElement[];
  background: string;
  width: number;
  height: number;
  viewport: ViewportState;
}

// Tool properties
export interface ToolProperties {
  color: string;
  strokeWidth: number;
  opacity: number;
  fontSize: number;
  fontFamily: string;
  filled: boolean;
  fillColor: string;
}

// User cursor
export interface UserCursor {
  userId: string;
  username: string;
  x: number;
  y: number;
  color: string;
  lastSeen: number;
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'drawing_action' | 'cursor_move' | 'user_join' | 'user_leave' | 'canvas_state' | 'undo' | 'redo' | 'clear';
  sessionId: string;
  userId: string;
  username?: string;
  data?: any;
  timestamp: number;
}

export interface DrawingActionMessage extends WebSocketMessage {
  type: 'drawing_action';
  data: {
    element: DrawingElement;
    action: 'add' | 'update' | 'remove';
  };
}

export interface CursorMoveMessage extends WebSocketMessage {
  type: 'cursor_move';
  data: {
    x: number;
    y: number;
  };
}

export interface UserJoinMessage extends WebSocketMessage {
  type: 'user_join';
  username: string;
}

export interface UserLeaveMessage extends WebSocketMessage {
  type: 'user_leave';
}

export interface CanvasStateMessage extends WebSocketMessage {
  type: 'canvas_state';
  data: {
    state: CanvasState;
  };
}

export interface UndoRedoMessage extends WebSocketMessage {
  type: 'undo' | 'redo';
  data: {
    elementId: string;
  };
}

export interface ClearMessage extends WebSocketMessage {
  type: 'clear';
}

// Whiteboard session
export interface WhiteboardSessionInfo {
  id: string;
  name: string;
  experienceId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  activeUsers: number;
}

// Drawing history for undo/redo
export interface HistoryState {
  elements: DrawingElement[];
  timestamp: number;
}

export interface DrawingHistory {
  states: HistoryState[];
  currentIndex: number;
  maxStates: number;
}

// Component props
export interface WhiteboardProps {
  sessionId: string;
  userId: string;
  username: string;
  experienceId: string;
  accessLevel: 'admin' | 'customer';
}

export interface ToolbarProps {
  currentTool: DrawingTool;
  toolProperties: ToolProperties;
  onToolChange: (tool: DrawingTool) => void;
  onPropertyChange: (properties: Partial<ToolProperties>) => void;
  onUndo: () => void;
  onRedo: () => void;
  onClear: () => void;
  onSave: () => void;
  canUndo: boolean;
  canRedo: boolean;
  viewport?: ViewportState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
}

export interface CanvasProps {
  sessionId: string;
  userId: string;
  username: string;
  currentTool: DrawingTool;
  toolProperties: ToolProperties;
  canvasState: CanvasState;
  activeUsers: UserCursor[];
  onDrawingAction: (element: DrawingElement, action: 'add' | 'update' | 'remove') => void;
  onCursorMove: (x: number, y: number) => void;
  onZoom: (delta: number, centerX?: number, centerY?: number) => void;
  onPan: (deltaX: number, deltaY: number) => void;
  onSelectionChange?: (selectedElements: string[]) => void;
}

export interface UserListProps {
  users: UserCursor[];
  currentUserId: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CreateSessionRequest {
  name: string;
  experienceId: string;
}

export interface CreateSessionResponse {
  sessionId: string;
}

export interface GetSessionResponse {
  session: WhiteboardSessionInfo;
  canvasState: CanvasState;
}

export interface GetSessionsResponse {
  sessions: WhiteboardSessionInfo[];
}

// Error types
export class WhiteboardError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'WhiteboardError';
  }
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Constants
export const DEFAULT_TOOL_PROPERTIES: ToolProperties = {
  color: '#000000',
  strokeWidth: 2,
  opacity: 1,
  fontSize: 16,
  fontFamily: 'Arial',
  filled: false,
  fillColor: '#ffffff',
};

export const DRAWING_TOOLS: DrawingTool[] = ['pen', 'eraser', 'rectangle', 'circle', 'line', 'text', 'select', 'pan'];

export const COLORS = [
  '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
  '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080',
  '#008000', '#800000', '#000080', '#808080', '#c0c0c0'
];

export const STROKE_WIDTHS = [1, 2, 4, 6, 8, 12, 16, 20];

export const FONT_SIZES = [12, 14, 16, 18, 20, 24, 28, 32, 36, 48];

export const FONT_FAMILIES = ['Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana', 'Georgia'];

// Zoom constants
export const MIN_ZOOM = 0.1;
export const MAX_ZOOM = 5.0;
export const DEFAULT_ZOOM = 1.0;
export const ZOOM_STEP = 0.1;

// Canvas constants for infinite canvas
export const INFINITE_CANVAS_SIZE = 10000; // Large canvas size for infinite feel
export const DEFAULT_VIEWPORT: ViewportState = {
  zoom: DEFAULT_ZOOM,
  panX: 0,
  panY: 0,
};
