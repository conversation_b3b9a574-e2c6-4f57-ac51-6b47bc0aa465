{"name": "whop-nextjs-app-template", "version": "0.1.0", "private": true, "scripts": {"dev": "whop-proxy --command 'next dev --turbopack'", "dev:no-proxy": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "@vercel/functions": "^2.0.3", "@whop/api": "^0.0.29", "@whop/react": "0.2.14", "fabric": "^6.4.3", "frosted-ui": "0.0.1-canary.76", "lucide-react": "^0.468.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^10.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@whop-apps/dev-proxy": "0.0.1-canary.116", "autoprefixer": "^10.4.20", "dotenv-cli": "^8.0.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}