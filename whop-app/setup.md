# Collaborative Whiteboard Setup Guide

## Quick Start

### 1. Environment Setup

Copy the environment template:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your Whop app credentials:
```env
WHOP_API_KEY="your_whop_api_key_here"
WHOP_WEBHOOK_SECRET="your_webhook_secret_here"
NEXT_PUBLIC_WHOP_AGENT_USER_ID="your_agent_user_id_here"
NEXT_PUBLIC_WHOP_APP_ID="your_app_id_here"
NEXT_PUBLIC_WHOP_COMPANY_ID="your_company_id_here"
```

### 2. Whop App Configuration

1. Go to [Whop Developer Dashboard](https://whop.com/dashboard/developer/)
2. Create a new app or use existing one
3. In the "Hosting" section:
   - **Base URL**: `http://localhost:3001/` (note the port 3001)
   - **App path**: `/experiences/[experienceId]`
   - **Discover path**: `/discover`

### 3. Run the Application

```bash
# Install dependencies (if not already done)
npm install --legacy-peer-deps

# Start the development server
PORT=3001 npm run dev
```

The app will be available at `http://localhost:3001`

### 4. Test the Whiteboard

1. Install your app in a Whop community
2. Navigate to the app in your community
3. Use the dev toggle (top right) to switch to localhost
4. Create a new whiteboard session
5. Start drawing!

## Features to Test

### Drawing Tools
- ✅ Pen tool with different colors and sizes
- ✅ Eraser tool
- ✅ Rectangle and circle shapes
- ✅ Line tool
- ✅ Text tool (double-click to add text)

### Collaboration
- ✅ Open multiple browser tabs/windows
- ✅ Draw in one tab and see it appear in others
- ✅ User cursors and presence indicators
- ✅ Real-time synchronization

### Session Management
- ✅ Create new whiteboard sessions
- ✅ Switch between sessions
- ✅ Delete sessions (admin/creator only)

### Persistence
- ✅ Save whiteboard state
- ✅ Reload and see saved content
- ✅ Undo/redo functionality

## Troubleshooting

### Port Issues
If port 3001 is in use, try a different port:
```bash
PORT=3002 npm run dev
```
Remember to update the Base URL in your Whop app settings.

### Supabase Connection Issues
- Check that your Supabase environment variables are set correctly
- Verify the Supabase Realtime connection status in the app header
- Check browser console for Supabase connection errors

### Database Issues
The app uses Supabase for the database. Make sure you've run the schema from `supabase-schema.sql` in your Supabase dashboard.

### Whop Integration Issues
- Verify all environment variables are set correctly
- Ensure the app is installed in your Whop community
- Check that you have the correct access level (admin/customer)

## Development Tips

### Testing Real-time Features
1. Open multiple browser tabs to the same session
2. Draw in one tab and verify it appears in others
3. Test cursor movement and user presence
4. Try different drawing tools and properties

### Database Inspection
The app uses Supabase for the database. You can inspect the database through the Supabase dashboard:
1. Go to your Supabase project dashboard
2. Navigate to the Table Editor
3. View the `whiteboard_sessions` and `drawing_actions` tables
4. Use the SQL Editor for custom queries

### Frontend-Only Architecture
The app now uses a frontend-only architecture with Supabase for database and real-time functionality. No custom server is needed.

## Next Steps

### Production Deployment
1. Choose any Next.js-compatible hosting platform (Vercel, Netlify, Railway, Render)
2. Your Supabase database is already production-ready
3. Set production environment variables
4. Update Whop app Base URL to your production domain

### Feature Extensions
- Add more drawing tools (brush patterns, stamps)
- Implement layers and layer management
- Add voice/video chat integration
- Create drawing templates and backgrounds
- Add export functionality (PNG, PDF, SVG)

## Support

- **Whop Documentation**: https://dev.whop.com/introduction
- **Next.js Documentation**: https://nextjs.org/docs
- **Socket.IO Documentation**: https://socket.io/docs/
