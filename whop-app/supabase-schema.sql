-- Supabase schema for Whiteboard app
-- Run this in your Supabase SQL editor

-- Create whiteboard_sessions table
CREATE TABLE IF NOT EXISTS whiteboard_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id TEXT NOT NULL,
  name TEXT NOT NULL,
  created_by TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  data JSONB DEFAULT '{}'::jsonb
);

-- Create drawing_actions table for real-time collaboration
CREATE TABLE IF NOT EXISTS drawing_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES whiteboard_sessions(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  username TEXT NOT NULL,
  action_type TEXT NOT NULL CHECK (action_type IN ('add', 'update', 'remove', 'clear')),
  element_data JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_whiteboard_sessions_experience_id ON whiteboard_sessions(experience_id);
CREATE INDEX IF NOT EXISTS idx_whiteboard_sessions_created_by ON whiteboard_sessions(created_by);
CREATE INDEX IF NOT EXISTS idx_drawing_actions_session_id ON drawing_actions(session_id);
CREATE INDEX IF NOT EXISTS idx_drawing_actions_created_at ON drawing_actions(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE whiteboard_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE drawing_actions ENABLE ROW LEVEL SECURITY;

-- Create policies for whiteboard_sessions
-- Allow all authenticated users to read sessions
CREATE POLICY "Allow authenticated users to read sessions" ON whiteboard_sessions
  FOR SELECT USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Allow all authenticated users to create sessions
CREATE POLICY "Allow authenticated users to create sessions" ON whiteboard_sessions
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Allow session creators to update their sessions
CREATE POLICY "Allow session creators to update sessions" ON whiteboard_sessions
  FOR UPDATE USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Allow session creators to delete their sessions
CREATE POLICY "Allow session creators to delete sessions" ON whiteboard_sessions
  FOR DELETE USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Create policies for drawing_actions
-- Allow all authenticated users to read drawing actions
CREATE POLICY "Allow authenticated users to read drawing actions" ON drawing_actions
  FOR SELECT USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Allow all authenticated users to create drawing actions
CREATE POLICY "Allow authenticated users to create drawing actions" ON drawing_actions
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_whiteboard_sessions_updated_at
  BEFORE UPDATE ON whiteboard_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
